#!/usr/bin/env swift

import Foundation

// 测试后端返回的实际JSON数据格式
let testJsonString = """
{
    "pets": [
        {
            "id": 17,
            "name": "<PERSON><PERSON>",
            "breed": "Sfa",
            "age": null,
            "age_display": "未知",
            "gender": "unknown",
            "color": null,
            "size": "medium",
            "weight": null,
            "avatar_url": null,
            "personality": null,
            "personality_tags": [],
            "current_mood": "happy",
            "mood_description": null,
            "response_style": "friendly",
            "interaction_count": 0,
            "experience_points": 0,
            "level": 1,
            "last_interaction_at": null,
            "owner_id": 1,
            "is_active": true,
            "created_at": "2025-07-02T06:56:22",
            "updated_at": "2025-07-02T06:56:22"
        }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "has_next": false
}
"""

// 定义数据模型（与iOS应用中的模型相同）
struct Pet: Codable, Identifiable {
    let id: Int
    let name: String
    let breed: String
    let age: Int?
    let ageDisplay: String
    let gender: String
    let color: String?
    let size: String
    let weight: Double?
    let avatarUrl: String?
    let personality: String?
    let personalityTags: [String]
    let currentMood: String
    let moodDescription: String?
    let responseStyle: String
    let interactionCount: Int
    let experiencePoints: Int
    let level: Int
    let lastInteractionAt: Date?
    let ownerId: Int
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id, name, breed, age, gender, color, size, weight, personality, level
        case ageDisplay = "age_display"
        case avatarUrl = "avatar_url"
        case personalityTags = "personality_tags"
        case currentMood = "current_mood"
        case moodDescription = "mood_description"
        case responseStyle = "response_style"
        case interactionCount = "interaction_count"
        case experiencePoints = "experience_points"
        case lastInteractionAt = "last_interaction_at"
        case ownerId = "owner_id"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct PetListResponse: Codable {
    let pets: [Pet]
    let total: Int
    let page: Int
    let size: Int
    let hasNext: Bool
    
    enum CodingKeys: String, CodingKey {
        case pets, total, page, size
        case hasNext = "has_next"
    }
}

// 创建多个日期格式化器来处理不同的日期格式（与修复后的NetworkManager相同）
func createDateFormatter(_ format: String) -> DateFormatter {
    let formatter = DateFormatter()
    formatter.dateFormat = format
    formatter.timeZone = TimeZone(secondsFromGMT: 0)
    formatter.locale = Locale(identifier: "en_US_POSIX")
    return formatter
}

func createJSONDecoder() -> JSONDecoder {
    let decoder = JSONDecoder()
    
    // 创建多个日期格式化器来处理不同的日期格式
    let formatters = [
        // 后端实际返回的格式：2025-07-02T06:56:22
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ss"),
        // 带微秒的格式：2025-07-02T06:56:22.123456
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"),
        // 带时区的格式：2025-07-02T06:56:22Z
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ss'Z'"),
        // 带微秒和时区的格式：2025-07-02T06:56:22.123456Z
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"),
        // ISO8601标准格式
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ssZ")
    ]
    
    decoder.dateDecodingStrategy = .custom { decoder in
        let container = try decoder.singleValueContainer()
        let dateString = try container.decode(String.self)
        
        // 尝试使用不同的格式化器解析日期
        for formatter in formatters {
            if let date = formatter.date(from: dateString) {
                return date
            }
        }
        
        // 如果所有格式都失败，抛出错误
        throw DecodingError.dataCorruptedError(
            in: container,
            debugDescription: "无法解析日期字符串: \(dateString)"
        )
    }
    
    return decoder
}

// 测试解析
func testDateParsingFix() {
    let data = testJsonString.data(using: .utf8)!
    
    do {
        let decoder = createJSONDecoder()
        let response = try decoder.decode(PetListResponse.self, from: data)
        
        print("✅ 日期解析修复测试成功!")
        print("📊 总数: \(response.total)")
        print("📄 页码: \(response.page)")
        print("🐾 宠物数量: \(response.pets.count)")
        
        if let firstPet = response.pets.first {
            print("🐾 第一只宠物:")
            print("   ID: \(firstPet.id)")
            print("   名称: \(firstPet.name)")
            print("   品种: \(firstPet.breed)")
            print("   创建时间: \(firstPet.createdAt)")
            print("   更新时间: \(firstPet.updatedAt)")
            print("   最后互动时间: \(firstPet.lastInteractionAt?.description ?? "无")")
        }
        
        print("\n🎉 JSON解析问题已修复！后端返回的日期格式现在可以正确解析了。")
        
    } catch {
        print("❌ 解析失败: \(error)")
        if let decodingError = error as? DecodingError {
            switch decodingError {
            case .dataCorrupted(let context):
                print("数据损坏: \(context.debugDescription)")
            case .keyNotFound(let key, let context):
                print("缺少键: \(key.stringValue), 上下文: \(context.debugDescription)")
            case .typeMismatch(let type, let context):
                print("类型不匹配: \(type), 上下文: \(context.debugDescription)")
            case .valueNotFound(let type, let context):
                print("值未找到: \(type), 上下文: \(context.debugDescription)")
            @unknown default:
                print("未知解析错误")
            }
        }
    }
}

// 运行测试
testDateParsingFix()
