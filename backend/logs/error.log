2025-06-30 23:53:45 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 00:08:40 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 00:11:43 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证凭据
2025-07-01 00:12:03 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证凭据
2025-07-01 00:13:11 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 创建动态失败
2025-07-01 00:17:33 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 创建动态失败
2025-07-01 00:20:58 | ERROR    | app.api.v1.feeds:create_feed:92 | 创建动态失败: cannot access local variable 'select' where it is not associated with a value
2025-07-01 00:20:58 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 创建动态失败: cannot access local variable 'select' where it is not associated with a value
2025-07-01 00:22:18 | ERROR    | app.api.v1.feeds:get_feeds:178 | 获取动态列表失败: 'AsyncSession' object has no attribute 'query'
2025-07-01 00:22:18 | ERROR    | app.api.v1.feeds:get_feeds:179 | 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/WorkSpace/furryKids/backend/app/api/v1/feeds.py", line 133, in get_feeds
    is_liked = feed_service.check_user_liked(feed.id, current_user.id)
  File "/Users/<USER>/WorkSpace/furryKids/backend/app/services/feed_service.py", line 233, in check_user_liked
    like = self.db.query(FeedLike).filter(
           ^^^^^^^^^^^^^
AttributeError: 'AsyncSession' object has no attribute 'query'

2025-07-01 00:22:18 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 获取动态列表失败: 'AsyncSession' object has no attribute 'query'
2025-07-01 00:27:34 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 00:45:14 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 00:48:15 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 00:49:14 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 400: {'success': False, 'message': '用户名已存在'}
2025-07-01 00:52:19 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 00:58:04 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 08:45:39 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:39:49 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:43:10 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:46:59 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:56:43 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:57:18 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:57:26 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:58:35 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 10:23:06 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 10:23:45 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 10:24:39 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 10:36:06 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 10:47:56 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证凭据
2025-07-01 10:48:06 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 10:48:41 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证凭据
2025-07-01 10:57:30 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:04:58 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:10:20 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:12:24 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:12:33 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:13:16 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:13:23 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:13:26 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:15:50 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证信息
2025-07-01 11:16:17 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证信息
2025-07-01 11:18:13 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:22:46 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:24:54 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:33:55 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:34:03 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:37:23 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:38:42 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:38:51 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:39:04 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:41:25 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 12:37:14 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 400: {'success': False, 'message': '用户名已存在'}
2025-07-01 12:37:38 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 400: {'success': False, 'message': '用户名已存在'}
2025-07-01 12:39:13 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 400: {'success': False, 'message': '用户名已存在'}
2025-07-01 12:39:14 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 'UNKNOWN' is not among the defined enum values. Enum name: petgender. Possible values: male, female, unknown
2025-07-01 12:39:14 | ERROR    | app.middleware.response_optimization:dispatch:70 | 请求处理异常: {'method': 'POST', 'url': 'http://localhost:3001/api/pets/', 'client_ip': '127.0.0.1', 'user_agent': 'python-requests/2.32.4'} - 'UNKNOWN' is not among the defined enum values. Enum name: petgender. Possible values: male, female, unknown
2025-07-01 12:40:54 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 400: {'success': False, 'message': '用户名已存在'}
2025-07-01 13:21:41 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证信息
2025-07-01 13:21:41 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 13:34:23 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证信息
2025-07-01 13:34:23 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 13:57:41 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 13:57:45 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 13:57:58 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 13:58:02 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 16:12:36 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 16:21:41 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证信息
2025-07-02 15:30:02 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:30:03 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:30:05 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:30:09 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:44:18 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:44:19 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:44:21 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:44:25 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:44:29 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:44:59 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:48:30 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 15:48:31 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 15:48:33 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 15:48:37 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 16:09:56 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 添加评论失败
2025-07-02 16:11:30 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 添加评论失败
2025-07-02 16:12:55 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 添加评论失败
2025-07-02 16:13:21 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 添加评论失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT feed_comments.id AS feed_comments_id, feed_comments.feed_id AS feed_comments_feed_id, feed_comments.user_id AS feed_comments_user_id, feed_comments.parent_id AS feed_comments_parent_id, feed_comments.content AS feed_comments_content, feed_comments.is_deleted AS feed_comments_is_deleted, feed_comments.is_hidden AS feed_comments_is_hidden, feed_comments.created_at AS feed_comments_created_at, feed_comments.updated_at AS feed_comments_updated_at 
FROM feed_comments 
WHERE %s = feed_comments.parent_id]
[parameters: [{'%(4614993296 param)s': 4}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-02 16:38:06 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': 'Not authenticated', 'code': 'NOT_AUTHENTICATED'}
2025-07-02 16:38:06 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': 'Not authenticated', 'code': 'NOT_AUTHENTICATED'}
2025-07-02 16:38:13 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': 'Not authenticated', 'code': 'NOT_AUTHENTICATED'}
2025-07-02 16:38:13 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': 'Not authenticated', 'code': 'NOT_AUTHENTICATED'}
2025-07-02 17:00:20 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证凭据
