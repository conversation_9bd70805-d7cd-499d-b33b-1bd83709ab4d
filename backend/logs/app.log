2025-06-30 23:48:17 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:48:17 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:48:17 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:48:17 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:48:17 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:48:17 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:48:17 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-06-30 23:48:31 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:48:31 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:48:31 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:48:31 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:48:31 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:48:31 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:48:31 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-06-30 23:50:26 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-06-30 23:51:33 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-06-30 23:51:33 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-06-30 23:51:33 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-06-30 23:51:34 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:51:34 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:51:34 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:51:34 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:51:34 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:51:34 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:51:34 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-06-30 23:51:51 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:51:51 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:51:51 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:51:51 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:51:51 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:51:51 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:51:51 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-06-30 23:53:45 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-06-30 23:55:06 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-06-30 23:55:06 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-06-30 23:55:06 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-06-30 23:55:07 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:55:07 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:55:07 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:55:07 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:55:07 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:55:07 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:55:07 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-06-30 23:56:03 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-06-30 23:56:03 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-06-30 23:56:03 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-06-30 23:56:05 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:56:05 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:56:05 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:56:05 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:56:05 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:56:05 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:56:05 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-06-30 23:56:42 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-06-30 23:56:42 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-06-30 23:56:42 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-06-30 23:56:43 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:56:43 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:56:43 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:56:44 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:56:44 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:56:44 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:56:44 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:00:19 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:00:19 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:00:19 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:00:20 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:00:20 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:00:20 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:00:20 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:00:20 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:00:20 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:00:20 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:06:27 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:06:27 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:06:27 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:06:28 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:06:28 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:06:28 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:06:29 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:06:29 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:06:29 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:06:29 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:08:40 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 00:10:31 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:10:31 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:10:31 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:10:44 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:10:44 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:10:44 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:10:44 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:10:44 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:10:44 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:10:44 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:11:05 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:11:05 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:11:05 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:11:24 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:11:24 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:11:24 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:11:24 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:11:24 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:11:24 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:11:24 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:11:43 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证凭据
2025-07-01 00:12:03 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证凭据
2025-07-01 00:13:11 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 创建动态失败
2025-07-01 00:13:59 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:13:59 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:13:59 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:14:01 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:14:01 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:14:01 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:14:01 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:14:01 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:14:01 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:14:01 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:15:06 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:15:06 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:15:06 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:15:07 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:15:07 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:15:07 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:15:07 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:15:07 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:15:07 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:15:07 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:17:33 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 创建动态失败
2025-07-01 00:18:28 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:18:28 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:18:28 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:18:30 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:18:30 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:18:30 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:18:30 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:18:30 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:18:30 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:18:30 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:20:58 | ERROR    | app.api.v1.feeds:create_feed:92 | 创建动态失败: cannot access local variable 'select' where it is not associated with a value
2025-07-01 00:20:58 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 创建动态失败: cannot access local variable 'select' where it is not associated with a value
2025-07-01 00:21:30 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:21:30 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:21:30 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:21:31 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:21:31 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:21:31 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:21:31 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:21:31 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:21:31 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:21:31 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:21:42 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:21:42 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:21:42 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:21:43 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:21:43 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:21:43 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:21:43 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:21:43 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:21:43 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:21:43 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:22:18 | ERROR    | app.api.v1.feeds:get_feeds:178 | 获取动态列表失败: 'AsyncSession' object has no attribute 'query'
2025-07-01 00:22:18 | ERROR    | app.api.v1.feeds:get_feeds:179 | 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/WorkSpace/furryKids/backend/app/api/v1/feeds.py", line 133, in get_feeds
    is_liked = feed_service.check_user_liked(feed.id, current_user.id)
  File "/Users/<USER>/WorkSpace/furryKids/backend/app/services/feed_service.py", line 233, in check_user_liked
    like = self.db.query(FeedLike).filter(
           ^^^^^^^^^^^^^
AttributeError: 'AsyncSession' object has no attribute 'query'

2025-07-01 00:22:18 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 获取动态列表失败: 'AsyncSession' object has no attribute 'query'
2025-07-01 00:24:49 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:24:49 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:24:49 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:24:51 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:24:51 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:24:51 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:24:51 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:24:51 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:24:51 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:24:51 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:25:01 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:25:01 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:25:01 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:25:02 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:25:02 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:25:02 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:25:02 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:25:02 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:25:02 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:25:02 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:27:34 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 00:44:59 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:44:59 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:44:59 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:44:59 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:44:59 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:44:59 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:44:59 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:45:14 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 00:48:15 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 00:49:14 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 400: {'success': False, 'message': '用户名已存在'}
2025-07-01 00:52:19 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 00:58:04 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 08:45:39 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 08:47:53 | WARNING  | app.middleware.response_optimization:dispatch:61 | 慢请求检测: GET /api/health 耗时 2.30s
2025-07-01 08:47:53 | WARNING  | app.main:add_process_time_header:111 | 🐌 慢请求警告 - GET /api/health 耗时: 2.44s
2025-07-01 09:39:49 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:43:10 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:46:59 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:56:43 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:57:18 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:57:26 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:58:35 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 10:23:06 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 10:23:45 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 10:24:39 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 10:33:50 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 10:33:50 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 10:33:50 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 10:33:50 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 10:33:50 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 10:33:50 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 10:33:50 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 10:36:06 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 10:46:05 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 10:46:05 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 10:46:05 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 10:46:07 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 10:46:07 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 10:46:07 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 10:46:07 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 10:46:07 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 10:46:07 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 10:46:07 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 10:46:27 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 10:46:27 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 10:46:27 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 10:46:28 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 10:46:28 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 10:46:28 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 10:46:28 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 10:46:28 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 10:46:28 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 10:46:28 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 10:46:40 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 10:46:40 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 10:46:40 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 10:46:41 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 10:46:41 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 10:46:41 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 10:46:41 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 10:46:41 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 10:46:41 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 10:46:41 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 10:46:55 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 10:46:55 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 10:46:55 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 10:46:57 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 10:46:57 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 10:46:57 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 10:46:57 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 10:46:57 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 10:46:57 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 10:46:57 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 10:47:21 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 10:47:22 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 10:47:22 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 10:47:22 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 10:47:22 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 10:47:22 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 10:47:22 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 10:47:56 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证凭据
2025-07-01 10:48:06 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 10:48:41 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证凭据
2025-07-01 10:57:30 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:04:58 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:10:20 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:12:24 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:12:33 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:13:08 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 11:13:08 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 11:13:08 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 11:13:09 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 11:13:09 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 11:13:09 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 11:13:10 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 11:13:10 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 11:13:10 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 11:13:10 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 11:13:16 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:13:21 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 11:13:21 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 11:13:21 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 11:13:22 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 11:13:22 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 11:13:22 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 11:13:22 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 11:13:22 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 11:13:22 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 11:13:22 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 11:13:23 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:13:26 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 11:13:36 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 11:13:36 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 11:13:36 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 11:13:37 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 11:13:37 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 11:13:37 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 11:13:37 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 11:13:37 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 11:13:37 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 11:13:37 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 11:13:57 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 11:13:57 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 11:13:57 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 11:13:59 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 11:13:59 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 11:13:59 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 11:13:59 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 11:13:59 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 11:13:59 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 11:13:59 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 11:14:27 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 11:14:27 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 11:14:27 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 11:15:28 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 11:15:28 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 11:15:28 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 11:15:28 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 11:15:28 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 11:15:28 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 11:15:28 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 11:15:50 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证信息
2025-07-01 11:16:17 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证信息
2025-07-01 11:18:13 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:22:46 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:24:54 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:33:55 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:34:03 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:37:23 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:38:42 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:38:51 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:39:04 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 11:41:25 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 12:32:50 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 12:32:50 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 12:32:50 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 12:32:50 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 12:32:50 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 12:32:50 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 12:32:50 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 12:33:45 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 12:33:45 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 12:33:45 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 12:33:53 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 12:33:53 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 12:33:53 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 12:33:53 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 12:33:53 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 12:33:53 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 12:33:53 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 12:36:23 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 12:36:23 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 12:36:23 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 12:36:25 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 12:36:25 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 12:36:25 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 12:36:25 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 12:36:25 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 12:36:25 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 12:36:25 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 12:37:03 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 12:37:03 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 12:37:03 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 12:37:04 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 12:37:05 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 12:37:05 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 12:37:05 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 12:37:05 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 12:37:05 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 12:37:05 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 12:37:14 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 400: {'success': False, 'message': '用户名已存在'}
2025-07-01 12:37:28 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 12:37:28 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 12:37:28 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 12:37:29 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 12:37:29 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 12:37:29 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 12:37:29 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 12:37:29 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 12:37:29 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 12:37:29 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 12:37:38 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 400: {'success': False, 'message': '用户名已存在'}
2025-07-01 12:38:40 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 12:38:40 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 12:38:40 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 12:38:41 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 12:38:41 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 12:38:41 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 12:38:42 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 12:38:42 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 12:38:42 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 12:38:42 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 12:38:50 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 12:38:50 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 12:38:50 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 12:38:51 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 12:38:52 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 12:38:52 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 12:38:52 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 12:38:52 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 12:38:52 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 12:38:52 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 12:39:02 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 12:39:02 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 12:39:02 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 12:39:03 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 12:39:03 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 12:39:03 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 12:39:03 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 12:39:03 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 12:39:03 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 12:39:03 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 12:39:13 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 400: {'success': False, 'message': '用户名已存在'}
2025-07-01 12:39:14 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 'UNKNOWN' is not among the defined enum values. Enum name: petgender. Possible values: male, female, unknown
2025-07-01 12:39:14 | ERROR    | app.middleware.response_optimization:dispatch:70 | 请求处理异常: {'method': 'POST', 'url': 'http://localhost:3001/api/pets/', 'client_ip': '127.0.0.1', 'user_agent': 'python-requests/2.32.4'} - 'UNKNOWN' is not among the defined enum values. Enum name: petgender. Possible values: male, female, unknown
2025-07-01 12:39:35 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 12:39:35 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 12:39:35 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 12:39:36 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 12:39:36 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 12:39:36 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 12:39:36 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 12:39:36 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 12:39:36 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 12:39:36 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 12:39:47 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 12:39:47 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 12:39:47 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 12:39:48 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 12:39:48 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 12:39:48 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 12:39:48 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 12:39:48 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 12:39:48 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 12:39:48 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 12:39:59 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 12:39:59 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 12:39:59 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 12:40:00 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 12:40:00 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 12:40:00 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 12:40:00 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 12:40:00 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 12:40:00 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 12:40:00 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 12:40:54 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 400: {'success': False, 'message': '用户名已存在'}
2025-07-01 13:21:32 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 13:21:32 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 13:21:32 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 13:21:33 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 13:21:33 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 13:21:33 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 13:21:33 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 13:21:33 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 13:21:33 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 13:21:33 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 13:21:41 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证信息
2025-07-01 13:21:41 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 13:34:23 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证信息
2025-07-01 13:34:23 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-01 13:57:41 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 13:57:45 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 13:57:58 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 13:58:02 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 16:12:36 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 16:21:41 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证信息
2025-07-02 13:45:24 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 13:45:25 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 13:45:25 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 13:45:25 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 13:45:25 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 13:45:25 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 13:45:25 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 13:47:05 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 13:47:05 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 13:47:05 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 13:47:33 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 13:47:33 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 13:47:33 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 13:47:33 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 13:47:33 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 13:47:33 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 13:47:33 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 14:44:26 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 14:44:26 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 14:44:26 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 14:44:26 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 14:44:26 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 14:44:26 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 14:44:26 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:30:02 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:30:03 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:30:05 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:30:09 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:44:18 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:44:19 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:44:21 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:44:25 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:44:29 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:44:59 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 点赞操作失败
2025-07-02 15:45:39 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:45:39 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:45:39 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:45:41 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:45:41 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:45:41 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:45:41 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:45:41 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:45:41 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:45:41 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:45:55 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:45:55 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:45:55 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:45:56 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:45:56 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:45:56 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:45:56 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:45:56 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:45:56 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:45:56 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:46:44 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:46:44 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:46:44 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:46:44 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:46:44 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:46:44 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:46:44 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:47:19 | WARNING  | app.main:add_process_time_header:111 | 🐌 慢请求警告 - POST /api/feeds/4/like 耗时: 1.09s
2025-07-02 15:48:30 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 15:48:31 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 15:48:33 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 15:48:37 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 15:50:19 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:50:19 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:50:19 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:50:19 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:50:19 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:50:19 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:50:21 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:50:21 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:50:21 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:50:21 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:50:21 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:50:21 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:50:21 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:50:21 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:50:21 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:50:21 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:50:21 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:50:21 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:50:21 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:50:21 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:50:38 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:50:38 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:50:38 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:50:38 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:50:38 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:50:38 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:50:40 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:50:40 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:50:40 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:50:40 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:50:40 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:50:40 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:50:40 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:50:40 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:50:40 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:50:40 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:50:40 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:50:40 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:50:40 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:50:40 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:50:52 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:50:52 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:50:52 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:50:52 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:50:52 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:50:52 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:50:55 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:50:55 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:50:55 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:50:55 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:50:55 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:50:55 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:50:55 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:50:55 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:50:55 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:50:55 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:50:55 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:50:55 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:50:55 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:50:55 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:51:05 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:51:05 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:51:05 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:51:05 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:51:05 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:51:05 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:51:07 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:51:07 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:51:07 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:51:07 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:51:07 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:51:07 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:51:07 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:51:07 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:51:07 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:51:07 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:51:07 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:51:07 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:51:07 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:51:07 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:51:19 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:51:19 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:51:19 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:51:19 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:51:19 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:51:19 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:51:20 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:51:20 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:51:20 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:51:20 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:51:20 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:51:20 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:51:20 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:51:20 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:51:20 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:51:20 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:51:20 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:51:20 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:51:20 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:51:20 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:51:42 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:51:42 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:51:42 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:51:42 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 15:51:42 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 15:51:42 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 15:51:44 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:51:44 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 15:51:44 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:51:44 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:51:44 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 15:51:44 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 15:51:44 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:51:44 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 15:51:44 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:51:44 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 15:51:44 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:51:44 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 15:51:44 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 15:51:44 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 16:09:56 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 添加评论失败
2025-07-02 16:10:35 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 16:10:35 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 16:10:35 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 16:10:36 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 16:10:36 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 16:10:36 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 16:10:37 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 16:10:37 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 16:10:37 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 16:10:37 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 16:10:56 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 16:10:56 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 16:10:56 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 16:10:56 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 16:10:56 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 16:10:56 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 16:10:56 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 16:11:30 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 添加评论失败
2025-07-02 16:12:33 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 16:12:33 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 16:12:33 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 16:12:33 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 16:12:33 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 16:12:33 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 16:12:35 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 16:12:35 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 16:12:35 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 16:12:35 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 16:12:35 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 16:12:35 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 16:12:35 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 16:12:35 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 16:12:35 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 16:12:35 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 16:12:35 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 16:12:35 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 16:12:35 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 16:12:35 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 16:12:55 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 添加评论失败
2025-07-02 16:13:07 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 16:13:07 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 16:13:07 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 16:13:07 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 16:13:07 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 16:13:07 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 16:13:09 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 16:13:09 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 16:13:09 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 16:13:09 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 16:13:09 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 16:13:09 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 16:13:09 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 16:13:09 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 16:13:09 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 16:13:09 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 16:13:09 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 16:13:09 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 16:13:09 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 16:13:09 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 16:13:21 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 添加评论失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT feed_comments.id AS feed_comments_id, feed_comments.feed_id AS feed_comments_feed_id, feed_comments.user_id AS feed_comments_user_id, feed_comments.parent_id AS feed_comments_parent_id, feed_comments.content AS feed_comments_content, feed_comments.is_deleted AS feed_comments_is_deleted, feed_comments.is_hidden AS feed_comments_is_hidden, feed_comments.created_at AS feed_comments_created_at, feed_comments.updated_at AS feed_comments_updated_at 
FROM feed_comments 
WHERE %s = feed_comments.parent_id]
[parameters: [{'%(4614993296 param)s': 4}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-02 16:13:56 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 16:13:56 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 16:13:56 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 16:13:56 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 16:13:56 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 16:13:56 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 16:13:57 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 16:13:57 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 16:13:57 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 16:13:57 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 16:13:57 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 16:13:57 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 16:13:57 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 16:13:57 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 16:13:57 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 16:13:57 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 16:13:57 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 16:13:57 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 16:13:57 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 16:13:57 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 16:20:57 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 16:20:57 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 16:20:57 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 16:20:57 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 16:20:57 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 16:20:57 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 16:20:59 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 16:20:59 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 16:20:59 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 16:20:59 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 16:20:59 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 16:20:59 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 16:20:59 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 16:20:59 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 16:20:59 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 16:20:59 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 16:20:59 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 16:20:59 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 16:20:59 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 16:20:59 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 16:21:28 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 16:21:28 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 16:21:28 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 16:21:28 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 16:21:28 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 16:21:28 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 16:21:28 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 16:38:06 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': 'Not authenticated', 'code': 'NOT_AUTHENTICATED'}
2025-07-02 16:38:06 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': 'Not authenticated', 'code': 'NOT_AUTHENTICATED'}
2025-07-02 16:38:13 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': 'Not authenticated', 'code': 'NOT_AUTHENTICATED'}
2025-07-02 16:38:13 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': 'Not authenticated', 'code': 'NOT_AUTHENTICATED'}
2025-07-02 16:43:26 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 16:43:26 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 16:43:26 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 16:43:26 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 16:43:26 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 16:43:26 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 16:43:26 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 17:00:20 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证凭据
2025-07-02 17:34:41 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 17:34:41 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 17:34:41 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 17:35:12 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 17:35:12 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 17:35:12 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 17:35:12 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 17:35:12 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 17:35:12 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 17:35:12 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 17:35:26 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 17:35:26 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 17:35:26 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 17:35:28 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 17:35:28 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 17:35:28 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-02 17:35:28 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 17:35:28 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-02 17:35:28 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-02 17:35:28 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 19:15:39 | WARNING  | app.middleware.response_optimization:dispatch:61 | 慢请求检测: GET /api/health 耗时 4.77s
2025-07-02 19:15:39 | WARNING  | app.main:add_process_time_header:111 | 🐌 慢请求警告 - GET /api/health 耗时: 4.80s
2025-07-02 22:57:46 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-02 22:57:46 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 22:57:46 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-02 22:57:50 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 22:57:50 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 22:57:50 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-02 22:57:50 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 22:57:50 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-02 22:57:50 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-02 22:57:50 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 22:58:23 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 22:58:23 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 22:58:23 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-02 22:58:23 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 22:58:23 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-02 22:58:23 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-02 22:58:23 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 23:05:17 | INFO     | app.main:lifespan:65 | 🛑 正在关闭服务...
2025-07-02 23:05:17 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 23:05:17 | INFO     | app.main:lifespan:67 | ✅ 服务已关闭
2025-07-02 23:05:37 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 23:05:37 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 23:05:37 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-02 23:05:37 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 23:05:37 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-02 23:05:37 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-02 23:05:37 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 23:08:46 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证凭据
2025-07-02 23:10:14 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 23:10:15 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 23:10:17 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 23:10:18 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 23:10:19 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 23:10:21 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 23:10:21 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 23:10:25 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 删除动态失败
2025-07-02 23:17:50 | INFO     | app.main:lifespan:65 | 🛑 正在关闭服务...
2025-07-02 23:17:50 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 23:17:50 | INFO     | app.main:lifespan:67 | ✅ 服务已关闭
2025-07-02 23:17:52 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 23:17:52 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 23:17:52 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-02 23:17:53 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 23:17:53 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-02 23:17:53 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-02 23:17:53 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 23:18:05 | INFO     | app.main:lifespan:65 | 🛑 正在关闭服务...
2025-07-02 23:18:05 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 23:18:05 | INFO     | app.main:lifespan:67 | ✅ 服务已关闭
2025-07-02 23:18:06 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 23:18:06 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 23:18:06 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-02 23:18:06 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 23:18:06 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-02 23:18:06 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-02 23:18:06 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 23:18:18 | INFO     | app.main:lifespan:65 | 🛑 正在关闭服务...
2025-07-02 23:18:18 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 23:18:18 | INFO     | app.main:lifespan:67 | ✅ 服务已关闭
2025-07-02 23:18:19 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 23:18:19 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 23:18:19 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-02 23:18:19 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 23:18:19 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-02 23:18:19 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-02 23:18:19 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-02 23:18:29 | INFO     | app.main:lifespan:65 | 🛑 正在关闭服务...
2025-07-02 23:18:29 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-02 23:18:29 | INFO     | app.main:lifespan:67 | ✅ 服务已关闭
2025-07-02 23:18:30 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-02 23:18:30 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-02 23:18:30 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-02 23:18:30 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-02 23:18:30 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-02 23:18:30 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-02 23:18:30 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-03 12:01:01 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-03 12:01:01 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-03 12:01:01 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-03 12:01:01 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-03 12:01:01 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-03 12:01:01 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-03 12:01:01 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-03 19:22:41 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': 'Not authenticated', 'code': 'NOT_AUTHENTICATED'}
2025-07-03 19:22:41 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': 'Not authenticated', 'code': 'NOT_AUTHENTICATED'}
2025-07-03 19:44:01 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-03 19:47:58 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-03 19:48:04 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-03 19:48:09 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-03 19:48:23 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-03 19:49:27 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-03 19:49:34 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-03 19:49:34 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-03 19:49:34 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-03 19:49:34 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-03 19:49:34 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-03 19:49:34 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-03 19:49:34 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-03 19:52:08 | INFO     | app.main:lifespan:65 | 🛑 正在关闭服务...
2025-07-03 19:52:08 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-03 19:52:08 | INFO     | app.main:lifespan:67 | ✅ 服务已关闭
2025-07-03 19:52:11 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-03 19:52:11 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-03 19:52:11 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-03 19:52:11 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-03 19:52:11 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-03 19:52:11 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-03 19:52:11 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-03 19:52:17 | INFO     | app.main:lifespan:65 | 🛑 正在关闭服务...
2025-07-03 19:52:17 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-03 19:52:17 | INFO     | app.main:lifespan:67 | ✅ 服务已关闭
2025-07-03 19:52:19 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-03 19:52:19 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-03 19:52:19 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-03 19:52:19 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-03 19:52:19 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-03 19:52:19 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-03 19:52:19 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-03 19:53:01 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-03 19:53:01 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-03 19:53:01 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-03 19:53:01 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-03 19:53:01 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-03 19:53:01 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-03 19:53:01 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-03 20:06:32 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 2.28s
2025-07-03 20:07:03 | WARNING  | app.middleware.response_optimization:dispatch:61 | 慢请求检测: GET /api/health 耗时 7.45s
2025-07-03 20:07:04 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 9.01s
2025-07-03 20:07:16 | WARNING  | app.middleware.response_optimization:dispatch:61 | 慢请求检测: GET /api/health 耗时 11.07s
2025-07-03 20:07:16 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 11.41s
2025-07-03 20:37:07 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 1.02s
2025-07-03 20:37:41 | WARNING  | app.middleware.response_optimization:dispatch:61 | 慢请求检测: GET /api/health 耗时 22.66s
2025-07-03 20:37:41 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 22.91s
2025-07-03 20:37:41 | WARNING  | app.middleware.response_optimization:dispatch:61 | 慢请求检测: GET /api/health 耗时 12.21s
2025-07-03 20:37:41 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 12.52s
2025-07-03 20:37:42 | WARNING  | app.middleware.response_optimization:dispatch:61 | 慢请求检测: GET /api/health 耗时 4.38s
2025-07-03 20:37:42 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 4.44s
2025-07-03 22:07:05 | WARNING  | app.middleware.response_optimization:dispatch:61 | 慢请求检测: GET /api/health 耗时 460.58s
2025-07-03 22:07:05 | WARNING  | app.middleware.response_optimization:dispatch:61 | 慢请求检测: GET /api/health 耗时 456.21s
2025-07-03 22:07:05 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 460.92s
2025-07-03 22:07:05 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 459.97s
2025-07-03 22:07:05 | WARNING  | app.middleware.response_optimization:dispatch:61 | 慢请求检测: GET /api/health 耗时 449.95s
2025-07-03 22:07:05 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 450.49s
2025-07-03 22:07:25 | WARNING  | app.middleware.response_optimization:dispatch:61 | 慢请求检测: GET /api/health 耗时 2.22s
2025-07-03 22:07:25 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 2.26s
2025-07-03 22:07:33 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 1.14s
2025-07-03 22:26:49 | WARNING  | app.middleware.response_optimization:dispatch:61 | 慢请求检测: GET /api/health 耗时 2.05s
2025-07-03 22:26:49 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 2.52s
2025-07-03 23:59:24 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - GET /api/health 耗时: 1.14s
2025-07-04 00:01:22 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-04 00:01:35 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-04 00:02:18 | WARNING  | app.main:add_process_time_header:113 | 🐌 慢请求警告 - POST /auth/login 耗时: 1.33s
2025-07-04 00:02:29 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 需要认证
2025-07-04 00:06:10 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 404: 宠物不存在
2025-07-04 00:17:17 | INFO     | app.main:lifespan:65 | 🛑 正在关闭服务...
2025-07-04 00:17:17 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-04 00:17:17 | INFO     | app.main:lifespan:67 | ✅ 服务已关闭
2025-07-04 00:17:19 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-04 00:17:19 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-04 00:17:19 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-04 00:17:19 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-04 00:17:19 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-04 00:17:19 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-04 00:17:19 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-04 00:18:22 | INFO     | app.main:lifespan:65 | 🛑 正在关闭服务...
2025-07-04 00:18:22 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-04 00:18:22 | INFO     | app.main:lifespan:67 | ✅ 服务已关闭
2025-07-04 00:18:24 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-04 00:18:24 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-04 00:18:24 | INFO     | app.main:lifespan:44 | ✅ 上传目录创建完成
2025-07-04 00:18:24 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-04 00:18:24 | INFO     | app.main:lifespan:48 | ✅ 数据库初始化完成
2025-07-04 00:18:24 | INFO     | app.main:lifespan:52 | ✅ 数据库连接正常
2025-07-04 00:18:24 | INFO     | app.main:lifespan:56 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
