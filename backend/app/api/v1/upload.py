"""
通用文件上传API路由
"""

import os
import uuid
import aiofiles
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth import get_current_user_required
from app.models.user import User
from app.core.config import settings

router = APIRouter(tags=["文件上传"])


@router.get("/health")
async def upload_health():
    """上传服务健康检查"""
    return {"status": "ok", "service": "upload"}


@router.post("/image")
async def upload_image(
    file: UploadFile = File(...),
    type: str = Form(...),  # feed, avatar, pet
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user_required)
):
    """
    通用图片上传
    
    - **file**: 图片文件（必填，支持jpg, jpeg, png, gif）
    - **type**: 图片类型（必填：feed, avatar, pet）
    
    文件会被保存到uploads/{type}/目录下
    """
    try:
        # 验证图片类型参数
        allowed_types = ["feed", "avatar", "pet"]
        if type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的图片类型，支持的类型: {', '.join(allowed_types)}"
            )

        # 验证文件类型
        allowed_content_types = ["image/jpeg", "image/jpg", "image/png", "image/gif"]
        if file.content_type not in allowed_content_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的文件类型，请上传jpg、png或gif格式的图片"
            )

        # 验证文件大小（10MB限制）
        max_size = settings.MAX_FILE_SIZE
        content = await file.read()
        file_size = len(content)
        
        if file_size > max_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"文件大小不能超过{max_size // (1024*1024)}MB"
            )

        # 创建上传目录
        upload_dir = f"uploads/{type}"
        os.makedirs(upload_dir, exist_ok=True)

        # 生成唯一文件名
        file_extension = file.filename.split('.')[-1] if '.' in file.filename else 'jpg'
        unique_filename = f"{uuid.uuid4()}.{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)

        # 保存文件
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(content)

        # 构建访问URL
        file_url = f"/uploads/{type}/{unique_filename}"

        return {
            "success": True,
            "data": {
                "url": file_url,
                "filename": unique_filename,
                "size": file_size,
                "type": type
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件上传失败: {str(e)}"
        )


@router.delete("/image")
async def delete_image(
    url: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user_required)
):
    """
    删除图片
    
    - **url**: 图片URL（必填）
    """
    try:
        # 验证URL格式
        if not url.startswith("/uploads/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的图片URL"
            )

        # 构建文件路径
        file_path = url[1:]  # 去掉开头的 /
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )

        # 删除文件
        os.remove(file_path)

        return {
            "success": True,
            "message": "文件删除成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件删除失败: {str(e)}"
        )
