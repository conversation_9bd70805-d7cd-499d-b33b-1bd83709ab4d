#!/usr/bin/env python3
"""
测试iOS端认证和宠物加载
"""

import requests
import json
import sys

def test_ios_auth_flow():
    """测试iOS端认证流程"""
    base_url = "http://localhost:3001"
    
    print("🧪 开始测试iOS端认证和宠物加载流程...")
    
    # 1. 健康检查
    print("\n🔍 1. 健康检查...")
    try:
        response = requests.get(f"{base_url}/api/health")
        if response.status_code == 200:
            print(f"✅ 健康检查通过: {response.json()}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False
    
    # 2. 用户登录
    print("\n🔐 2. 用户登录...")
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(f"{base_url}/auth/login", json=login_data)
        if response.status_code == 200:
            login_result = response.json()
            print(f"✅ 登录成功")
            print(f"   用户: {login_result.get('user', {}).get('username')}")
            
            # 检查token格式
            token = login_result.get("access_token")
            if token:
                print(f"   Token: {token[:20]}...")
                print(f"   Token长度: {len(token)}")
            else:
                print("❌ 未获取到access_token")
                return False
                
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 3. 测试宠物列表API（使用token）
    print("\n🐾 3. 测试宠物列表API...")
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{base_url}/api/pets/", headers=headers)
        print(f"   请求URL: {base_url}/api/pets/")
        print(f"   请求头: Authorization: Bearer {token[:20]}...")
        print(f"   响应状态: {response.status_code}")
        
        if response.status_code == 200:
            pets_data = response.json()
            pets = pets_data.get("pets", [])
            print(f"✅ 宠物列表获取成功")
            print(f"   宠物数量: {len(pets)}")
            
            for i, pet in enumerate(pets[:3]):  # 只显示前3个
                print(f"   宠物{i+1}: ID={pet.get('id')}, 名称={pet.get('name')}, 品种={pet.get('breed')}")
                
            return True
        else:
            print(f"❌ 宠物列表获取失败: {response.status_code}")
            print(f"   错误详情: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 宠物列表请求异常: {e}")
        return False

def test_token_validation():
    """测试token验证"""
    base_url = "http://localhost:3001"
    
    print("\n🔍 4. 测试token验证...")
    
    # 使用错误的token
    fake_token = "fake_token_12345"
    headers = {
        "Authorization": f"Bearer {fake_token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{base_url}/api/pets/", headers=headers)
        print(f"   使用假token的响应状态: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ Token验证正常工作（正确拒绝了假token）")
        else:
            print(f"⚠️ Token验证可能有问题（状态码: {response.status_code}）")
            
    except Exception as e:
        print(f"❌ Token验证测试异常: {e}")

def test_no_token():
    """测试无token请求"""
    base_url = "http://localhost:3001"
    
    print("\n🔍 5. 测试无token请求...")
    
    try:
        response = requests.get(f"{base_url}/api/pets/")
        print(f"   无token请求的响应状态: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ 无token请求正确被拒绝")
        else:
            print(f"⚠️ 无token请求处理可能有问题（状态码: {response.status_code}）")
            
    except Exception as e:
        print(f"❌ 无token请求测试异常: {e}")

if __name__ == "__main__":
    print("🧪 开始iOS端认证流程测试...")
    
    success = test_ios_auth_flow()
    test_token_validation()
    test_no_token()
    
    if success:
        print("\n🎉 测试完成 - 认证流程正常!")
        print("\n💡 如果iOS端仍然无法显示宠物，请检查:")
        print("   1. iOS端是否正确保存了token")
        print("   2. iOS端是否在请求中正确添加了Authorization头")
        print("   3. iOS端的API请求URL是否正确")
        print("   4. iOS端的JSON解析是否正确")
        sys.exit(0)
    else:
        print("\n💥 测试失败 - 请检查后端服务")
        sys.exit(1)
