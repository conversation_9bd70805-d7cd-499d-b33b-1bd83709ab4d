#!/usr/bin/env python3
"""
测试宠物创建API
"""

import requests
import json
import sys

def test_pet_creation():
    """测试宠物创建功能"""
    base_url = "http://localhost:3001"
    
    # 1. 首先测试健康检查
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/api/health")
        print(f"✅ 健康检查: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False
    
    # 2. 创建测试用户（如果需要）
    print("\n👤 创建测试用户...")
    user_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    try:
        # 尝试注册
        response = requests.post(f"{base_url}/auth/register", json=user_data)
        if response.status_code in [200, 201]:
            print(f"✅ 用户注册成功: {response.json()}")
        elif response.status_code == 400:
            print("ℹ️ 用户可能已存在，尝试登录...")
        else:
            print(f"⚠️ 注册响应: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ 用户注册失败: {e}")

    # 3. 用户登录获取token
    print("\n🔐 用户登录...")
    try:
        response = requests.post(f"{base_url}/auth/login", json=user_data)
        if response.status_code == 200:
            login_result = response.json()
            print(f"✅ 登录成功: {login_result}")
            token = login_result.get("access_token")
            if not token:
                print("❌ 未获取到访问令牌")
                return False
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return False
    
    # 4. 创建宠物
    print("\n🐾 创建测试宠物...")
    pet_data = {
        "name": "测试小狗",
        "breed": "柴犬",
        "age": 24,
        "gender": "unknown",
        "color": "金黄色",
        "size": "medium",
        "weight": 15.5,
        "personality": "活泼可爱",
        "personality_tags": ["活泼", "友善"],
        "current_mood": "happy",
        "response_style": "friendly"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"📡 发送请求到: {base_url}/api/pets/")
        print(f"📋 请求数据: {json.dumps(pet_data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(f"{base_url}/api/pets/", json=pet_data, headers=headers)
        print(f"📨 响应状态码: {response.status_code}")
        print(f"📨 响应头: {dict(response.headers)}")
        
        if response.status_code in [200, 201]:
            result = response.json()
            print(f"✅ 宠物创建成功:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return True
        else:
            print(f"❌ 宠物创建失败: {response.status_code}")
            print(f"错误详情: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 宠物创建请求失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试宠物创建API...")
    success = test_pet_creation()
    if success:
        print("\n🎉 测试完成 - 所有功能正常!")
        sys.exit(0)
    else:
        print("\n💥 测试失败 - 请检查错误信息")
        sys.exit(1)
