#!/usr/bin/env swift

import Foundation

// 测试后端返回的实际Feed JSON数据格式
let testFeedJsonString = """
{
    "feeds": [
        {
            "content": "Mishaps ",
            "images": [],
            "mood": null,
            "tags": [],
            "location": null,
            "is_public": true,
            "id": 3,
            "pet_id": 17,
            "user_id": 1,
            "likes_count": 0,
            "comments_count": 0,
            "shares_count": 0,
            "views_count": 0,
            "ai_generated_content": null,
            "ai_mood_score": null,
            "ai_quality_score": null,
            "ai_tags": null,
            "status": "published",
            "is_featured": false,
            "is_ai_generated": false,
            "published_at": "2025-07-02T07:07:58",
            "created_at": "2025-07-02T07:07:58",
            "updated_at": null,
            "is_liked": false,
            "pet_name": "<PERSON><PERSON>",
            "pet_avatar": null,
            "user_name": "testuser"
        }
    ],
    "total": 1,
    "page": 1,
    "size": 20,
    "has_more": false
}
"""

// 定义Pet数据模型（简化版）
struct Pet: Codable, Identifiable {
    let id: Int
    let name: String
    let breed: String
    let age: Int?
    let ageDisplay: String
    let gender: String
    let color: String?
    let size: String
    let weight: Double?
    let avatarUrl: String?
    let personality: String?
    let personalityTags: [String]
    let currentMood: String
    let moodDescription: String?
    let responseStyle: String
    let interactionCount: Int
    let experiencePoints: Int
    let level: Int
    let lastInteractionAt: Date?
    let ownerId: Int
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id, name, breed, age, gender, color, size, weight, personality, level
        case ageDisplay = "age_display"
        case avatarUrl = "avatar_url"
        case personalityTags = "personality_tags"
        case currentMood = "current_mood"
        case moodDescription = "mood_description"
        case responseStyle = "response_style"
        case interactionCount = "interaction_count"
        case experiencePoints = "experience_points"
        case lastInteractionAt = "last_interaction_at"
        case ownerId = "owner_id"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// 定义Feed数据模型（与修复后的iOS模型相同）
struct Feed: Codable, Identifiable {
    let id: Int
    let petId: Int
    let userId: Int
    let content: String
    let images: [String]
    let tags: [String]
    let mood: String?
    let location: String?
    let isPublic: Bool
    let status: String
    var likesCount: Int
    var commentsCount: Int
    var sharesCount: Int
    let viewsCount: Int
    var isLiked: Bool
    let createdAt: Date
    let updatedAt: Date?    // 修改为可选类型
    
    // 后端返回的额外字段
    let aiGeneratedContent: String?
    let aiMoodScore: Double?
    let aiQualityScore: Double?
    let aiTags: [String]?
    let isFeatured: Bool
    let isAiGenerated: Bool
    let publishedAt: Date?
    let petName: String?
    let petAvatar: String?
    let userName: String?

    // 关联数据
    let pet: Pet?

    // 编码键映射
    enum CodingKeys: String, CodingKey {
        case id, content, images, tags, mood, location, status, pet
        case isPublic = "is_public"
        case petId = "pet_id"
        case userId = "user_id"
        case likesCount = "likes_count"
        case commentsCount = "comments_count"
        case sharesCount = "shares_count"
        case viewsCount = "views_count"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case isLiked = "is_liked"
        case aiGeneratedContent = "ai_generated_content"
        case aiMoodScore = "ai_mood_score"
        case aiQualityScore = "ai_quality_score"
        case aiTags = "ai_tags"
        case isFeatured = "is_featured"
        case isAiGenerated = "is_ai_generated"
        case publishedAt = "published_at"
        case petName = "pet_name"
        case petAvatar = "pet_avatar"
        case userName = "user_name"
    }
}

struct FeedListResponse: Codable {
    let feeds: [Feed]
    let total: Int
    let page: Int
    let size: Int
    let hasMore: Bool
    
    enum CodingKeys: String, CodingKey {
        case feeds, total, page, size
        case hasMore = "has_more"
    }
}

// 创建多个日期格式化器来处理不同的日期格式
func createDateFormatter(_ format: String) -> DateFormatter {
    let formatter = DateFormatter()
    formatter.dateFormat = format
    formatter.timeZone = TimeZone(secondsFromGMT: 0)
    formatter.locale = Locale(identifier: "en_US_POSIX")
    return formatter
}

func createJSONDecoder() -> JSONDecoder {
    let decoder = JSONDecoder()
    
    // 创建多个日期格式化器来处理不同的日期格式
    let formatters = [
        // 后端实际返回的格式：2025-07-02T06:56:22
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ss"),
        // 带微秒的格式：2025-07-02T06:56:22.123456
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"),
        // 带时区的格式：2025-07-02T06:56:22Z
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ss'Z'"),
        // 带微秒和时区的格式：2025-07-02T06:56:22.123456Z
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"),
        // ISO8601标准格式
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ssZ")
    ]
    
    decoder.dateDecodingStrategy = .custom { decoder in
        let container = try decoder.singleValueContainer()
        let dateString = try container.decode(String.self)
        
        // 尝试使用不同的格式化器解析日期
        for formatter in formatters {
            if let date = formatter.date(from: dateString) {
                return date
            }
        }
        
        // 如果所有格式都失败，抛出错误
        throw DecodingError.dataCorruptedError(
            in: container,
            debugDescription: "无法解析日期字符串: \(dateString)"
        )
    }
    
    return decoder
}

// 测试解析
func testFeedParsing() {
    let data = testFeedJsonString.data(using: .utf8)!
    
    do {
        let decoder = createJSONDecoder()
        let response = try decoder.decode(FeedListResponse.self, from: data)
        
        print("✅ Feed JSON解析成功!")
        print("📊 总数: \(response.total)")
        print("📄 页码: \(response.page)")
        print("📝 动态数量: \(response.feeds.count)")
        
        if let firstFeed = response.feeds.first {
            print("📝 第一条动态:")
            print("   ID: \(firstFeed.id)")
            print("   内容: \(firstFeed.content)")
            print("   宠物ID: \(firstFeed.petId)")
            print("   用户ID: \(firstFeed.userId)")
            print("   是否公开: \(firstFeed.isPublic)")
            print("   点赞数: \(firstFeed.likesCount)")
            print("   创建时间: \(firstFeed.createdAt)")
            print("   更新时间: \(firstFeed.updatedAt?.description ?? "无")")
            print("   发布时间: \(firstFeed.publishedAt?.description ?? "无")")
            print("   宠物名称: \(firstFeed.petName ?? "无")")
            print("   用户名: \(firstFeed.userName ?? "无")")
        }
        
        print("\n🎉 Feed数据解析问题已修复！")
        
    } catch {
        print("❌ 解析失败: \(error)")
        if let decodingError = error as? DecodingError {
            switch decodingError {
            case .dataCorrupted(let context):
                print("数据损坏: \(context.debugDescription)")
            case .keyNotFound(let key, let context):
                print("缺少键: \(key.stringValue), 上下文: \(context.debugDescription)")
            case .typeMismatch(let type, let context):
                print("类型不匹配: \(type), 上下文: \(context.debugDescription)")
            case .valueNotFound(let type, let context):
                print("值未找到: \(type), 上下文: \(context.debugDescription)")
            @unknown default:
                print("未知解析错误")
            }
        }
    }
}

// 运行测试
testFeedParsing()
