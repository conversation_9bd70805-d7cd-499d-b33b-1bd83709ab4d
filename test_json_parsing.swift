import Foundation

// 测试JSON解析是否修复
let jsonString = """
{
  "feeds": [
    {
      "content": "小白今天很开心，在公园里跑来跑去！🐕",
      "images": [],
      "mood": "开心",
      "tags": [],
      "location": null,
      "is_public": true,
      "id": 2,
      "pet_id": 6,
      "user_id": 6,
      "likes_count": 0,
      "comments_count": 0,
      "shares_count": 0,
      "views_count": 0,
      "ai_generated_content": null,
      "ai_mood_score": null,
      "ai_quality_score": null,
      "ai_tags": null,
      "status": "published",
      "is_featured": false,
      "is_ai_generated": false,
      "published_at": "2025-06-30T16:21:55",
      "created_at": "2025-06-30T16:21:55",
      "updated_at": null,
      "is_liked": false,
      "pet_name": "小白",
      "pet_avatar": null,
      "user_name": "test_ios"
    },
    {
      "content": "今天和小白去公园玩了！天气很好，小白很开心！",
      "images": [],
      "mood": "开心",
      "tags": [],
      "location": null,
      "is_public": true,
      "id": 1,
      "pet_id": 6,
      "user_id": 6,
      "likes_count": 0,
      "comments_count": 0,
      "shares_count": 0,
      "views_count": 0,
      "ai_generated_content": null,
      "ai_mood_score": null,
      "ai_quality_score": null,
      "ai_tags": null,
      "status": "published",
      "is_featured": false,
      "is_ai_generated": false,
      "published_at": "2025-06-30T16:13:11",
      "created_at": "2025-06-30T16:13:10",
      "updated_at": null,
      "is_liked": false,
      "pet_name": "小白",
      "pet_avatar": null,
      "user_name": "test_ios"
    }
  ],
  "total": 2,
  "page": 1,
  "size": 5,
  "has_more": false
}
"""

// 定义数据模型（与iOS应用中的模型相同）
struct Feed: Codable {
    let id: Int
    let petId: Int
    let userId: Int
    let content: String
    let images: [String]
    let tags: [String]
    let mood: String?
    let location: String?
    let isPublic: Bool      // 修复后：使用is_public
    let status: String
    var likesCount: Int
    var commentsCount: Int
    var sharesCount: Int
    let viewsCount: Int
    let createdAt: Date
    let updatedAt: Date?
    let pet: Pet?

    // 编码键映射
    enum CodingKeys: String, CodingKey {
        case id, content, images, tags, mood, location, status, pet
        case isPublic = "is_public"
        case petId = "pet_id"
        case userId = "user_id"
        case likesCount = "likes_count"
        case commentsCount = "comments_count"
        case sharesCount = "shares_count"
        case viewsCount = "views_count"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct Pet: Codable {
    let id: Int
    let name: String
    let avatar: String?
}

struct FeedListResponse: Codable {
    let feeds: [Feed]
    let total: Int
    let page: Int
    let size: Int
    let hasMore: Bool
    
    enum CodingKeys: String, CodingKey {
        case feeds, total, page, size
        case hasMore = "has_more"
    }
}

// 测试解析
func testJSONParsing() {
    let data = jsonString.data(using: .utf8)!
    
    do {
        let decoder = JSONDecoder()
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        decoder.dateDecodingStrategy = .formatted(dateFormatter)
        
        let response = try decoder.decode(FeedListResponse.self, from: data)
        
        print("✅ JSON解析成功!")
        print("📊 总数: \(response.total)")
        print("📄 页码: \(response.page)")
        print("📝 动态数量: \(response.feeds.count)")
        
        if let firstFeed = response.feeds.first {
            print("🐾 第一条动态:")
            print("   ID: \(firstFeed.id)")
            print("   内容: \(firstFeed.content)")
            print("   是否公开: \(firstFeed.isPublic)")
            print("   点赞数: \(firstFeed.likesCount)")
            print("   创建时间: \(firstFeed.createdAt)")
        }
        
    } catch {
        print("❌ JSON解析失败: \(error)")
        if let decodingError = error as? DecodingError {
            switch decodingError {
            case .keyNotFound(let key, let context):
                print("   缺少键: \(key.stringValue)")
                print("   上下文: \(context.debugDescription)")
            case .typeMismatch(let type, let context):
                print("   类型不匹配: \(type)")
                print("   上下文: \(context.debugDescription)")
            case .valueNotFound(let type, let context):
                print("   值未找到: \(type)")
                print("   上下文: \(context.debugDescription)")
            case .dataCorrupted(let context):
                print("   数据损坏: \(context.debugDescription)")
            @unknown default:
                print("   未知解码错误")
            }
        }
    }
}

// 运行测试
testJSONParsing()
