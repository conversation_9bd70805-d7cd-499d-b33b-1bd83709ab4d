import Foundation

// 测试完整的登录和宠物加载流程
func testFullFlow() {
    print("🚀 开始测试完整流程...")
    
    // 1. 测试登录
    print("\n1️⃣ 测试登录...")
    testLogin()
    
    // 2. 测试宠物列表获取
    print("\n2️⃣ 测试宠物列表获取...")
    testPetList()
    
    // 3. 测试动态列表获取
    print("\n3️⃣ 测试动态列表获取...")
    testFeedList()
}

func testLogin() {
    let url = URL(string: "http://10.36.198.123:3001/auth/login")!
    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    
    let loginData = [
        "username": "testuser",
        "password": "testpass123"
    ]
    
    do {
        request.httpBody = try JSONSerialization.data(withJSONObject: loginData)
        
        let semaphore = DispatchSemaphore(value: 0)
        var result: (Data?, URLResponse?, Error?) = (nil, nil, nil)
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            result = (data, response, error)
            semaphore.signal()
        }.resume()
        
        semaphore.wait()
        
        if let error = result.2 {
            print("❌ 登录请求失败: \(error)")
            return
        }
        
        if let data = result.0 {
            if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                if let success = json["success"] as? Bool, success {
                    if let token = json["access_token"] as? String {
                        print("✅ 登录成功，获得token: \(token.prefix(20))...")
                        // 保存token用于后续请求
                        UserDefaults.standard.set(token, forKey: "auth_token")
                    }
                } else {
                    print("❌ 登录失败: \(json)")
                }
            }
        }
    } catch {
        print("❌ 登录数据序列化失败: \(error)")
    }
}

func testPetList() {
    guard let token = UserDefaults.standard.string(forKey: "auth_token") else {
        print("❌ 没有认证token，无法测试宠物列表")
        return
    }
    
    let url = URL(string: "http://10.36.198.123:3001/api/pets/?page=1&size=5")!
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
    
    let semaphore = DispatchSemaphore(value: 0)
    var result: (Data?, URLResponse?, Error?) = (nil, nil, nil)
    
    URLSession.shared.dataTask(with: request) { data, response, error in
        result = (data, response, error)
        semaphore.signal()
    }.resume()
    
    semaphore.wait()
    
    if let error = result.2 {
        print("❌ 宠物列表请求失败: \(error)")
        return
    }
    
    if let httpResponse = result.1 as? HTTPURLResponse {
        print("📡 HTTP状态码: \(httpResponse.statusCode)")
    }
    
    if let data = result.0 {
        if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            if let pets = json["pets"] as? [[String: Any]] {
                print("✅ 成功获取宠物列表，数量: \(pets.count)")
                for (index, pet) in pets.enumerated() {
                    if let name = pet["name"] as? String,
                       let breed = pet["breed"] as? String,
                       let id = pet["id"] as? Int {
                        print("   🐕 宠物\(index + 1): ID=\(id), 名称=\(name), 品种=\(breed)")
                    }
                }
            } else {
                print("❌ 宠物列表格式错误: \(json)")
            }
        } else {
            print("❌ 宠物列表JSON解析失败")
            if let dataString = String(data: data, encoding: .utf8) {
                print("原始响应: \(dataString)")
            }
        }
    }
}

func testFeedList() {
    guard let token = UserDefaults.standard.string(forKey: "auth_token") else {
        print("❌ 没有认证token，无法测试动态列表")
        return
    }
    
    let url = URL(string: "http://10.36.198.123:3001/api/feeds/?page=1&size=5")!
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
    
    let semaphore = DispatchSemaphore(value: 0)
    var result: (Data?, URLResponse?, Error?) = (nil, nil, nil)
    
    URLSession.shared.dataTask(with: request) { data, response, error in
        result = (data, response, error)
        semaphore.signal()
    }.resume()
    
    semaphore.wait()
    
    if let error = result.2 {
        print("❌ 动态列表请求失败: \(error)")
        return
    }
    
    if let httpResponse = result.1 as? HTTPURLResponse {
        print("📡 HTTP状态码: \(httpResponse.statusCode)")
    }
    
    if let data = result.0 {
        if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            if let feeds = json["feeds"] as? [[String: Any]] {
                print("✅ 成功获取动态列表，数量: \(feeds.count)")
                for (index, feed) in feeds.enumerated() {
                    if let content = feed["content"] as? String,
                       let id = feed["id"] as? Int {
                        print("   📝 动态\(index + 1): ID=\(id), 内容=\(content.prefix(20))...")
                    }
                }
            } else {
                print("❌ 动态列表格式错误: \(json)")
            }
        } else {
            print("❌ 动态列表JSON解析失败")
            if let dataString = String(data: data, encoding: .utf8) {
                print("原始响应: \(dataString)")
            }
        }
    }
}

// 运行测试
testFullFlow()
