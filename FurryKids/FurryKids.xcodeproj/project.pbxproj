// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		0001000100010001001000010 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000011 /* ContentView.swift */; };
		0001000100010001001000020 /* FurryKidsApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000021 /* FurryKidsApp.swift */; };
		0001000100010001001000030 /* Pet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000031 /* Pet.swift */; };
		0001000100010001001000040 /* Feed.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000041 /* Feed.swift */; };
		0001000100010001001000050 /* Message.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000051 /* Message.swift */; };
		0001000100010001001000060 /* Color+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000061 /* Color+Extensions.swift */; };
		0001000100010001001000070 /* FeedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000071 /* FeedView.swift */; };
		0001000100010001001000080 /* ProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000081 /* ProfileView.swift */; };
		0001000100010001001000090 /* InteractionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000091 /* InteractionView.swift */; };
		0001000100010001001000100 /* CreateFeedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000101 /* CreateFeedView.swift */; };
		0001000100010001001000200 /* AddPetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000201 /* AddPetView.swift */; };
		0001000100010001001000210 /* PetDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000211 /* PetDetailView.swift */; };
		0001000100010001001000220 /* FeedCommentsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000221 /* FeedCommentsView.swift */; };
		0001000100010001001000225 /* EditFeedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000226 /* EditFeedView.swift */; };
		0001000100010001001000230 /* CommentService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000231 /* CommentService.swift */; };
		0001000100010001001000110 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000111 /* Assets.xcassets */; };
		0001000100010001001000120 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000121 /* Preview Assets.xcassets */; };

		0001000100010001001000150 /* InteractionStore.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000151 /* InteractionStore.swift */; };
		0001000100010001001000152 /* AuthStore.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000153 /* AuthStore.swift */; };
		0001000100010001001000154 /* PetStore.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000155 /* PetStore.swift */; };
		0001000100010001001000156 /* FeedStore.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000157 /* FeedStore.swift */; };
		0001000100010001001000170 /* SpeechHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000171 /* SpeechHelper.swift */; };
		0001000100010001001000180 /* NetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000181 /* NetworkManager.swift */; };
		0001000100010001001000190 /* AIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000191 /* AIService.swift */; };
		0001000100010001001000200 /* DataService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000201 /* DataService.swift */; };
		0001000100010001001000210 /* AuthService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000211 /* AuthService.swift */; };
		0001000100010001001000212 /* PetService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000213 /* PetService.swift */; };
		0001000100010001001000214 /* FeedService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000215 /* FeedService.swift */; };

		0001000100010001001000250 /* User.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000251 /* User.swift */; };
		0001000100010001001000260 /* AIModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000261 /* AIModels.swift */; };
		0001000100010001001000262 /* PetEnums.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0001000100010001001000263 /* PetEnums.swift */; };

		0001000100010001001000160 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0001000100010001000100001 /* FurryKids.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FurryKids.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0001000100010001001000011 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		0001000100010001001000021 /* FurryKidsApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FurryKidsApp.swift; sourceTree = "<group>"; };
		0001000100010001001000031 /* Pet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Pet.swift; sourceTree = "<group>"; };
		0001000100010001001000041 /* Feed.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Feed.swift; sourceTree = "<group>"; };
		0001000100010001001000051 /* Message.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Message.swift; sourceTree = "<group>"; };
		0001000100010001001000061 /* Color+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Color+Extensions.swift"; sourceTree = "<group>"; };
		0001000100010001001000071 /* FeedView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeedView.swift; sourceTree = "<group>"; };
		0001000100010001001000081 /* ProfileView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileView.swift; sourceTree = "<group>"; };
		0001000100010001001000091 /* InteractionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InteractionView.swift; sourceTree = "<group>"; };
		0001000100010001001000101 /* CreateFeedView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateFeedView.swift; sourceTree = "<group>"; };
		0001000100010001001000201 /* AddPetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddPetView.swift; sourceTree = "<group>"; };
		0001000100010001001000211 /* PetDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PetDetailView.swift; sourceTree = "<group>"; };
		0001000100010001001000221 /* FeedCommentsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeedCommentsView.swift; sourceTree = "<group>"; };
		0001000100010001001000226 /* EditFeedView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditFeedView.swift; sourceTree = "<group>"; };
		0001000100010001001000231 /* CommentService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentService.swift; sourceTree = "<group>"; };
		0001000100010001001000111 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		0001000100010001001000121 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };

		0001000100010001001000151 /* InteractionStore.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InteractionStore.swift; sourceTree = "<group>"; };
		0001000100010001001000153 /* AuthStore.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthStore.swift; sourceTree = "<group>"; };
		0001000100010001001000155 /* PetStore.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PetStore.swift; sourceTree = "<group>"; };
		0001000100010001001000157 /* FeedStore.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeedStore.swift; sourceTree = "<group>"; };
		0001000100010001001000171 /* SpeechHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SpeechHelper.swift; sourceTree = "<group>"; };
		0001000100010001001000181 /* NetworkManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkManager.swift; sourceTree = "<group>"; };
		0001000100010001001000191 /* AIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIService.swift; sourceTree = "<group>"; };
		0001000100010001001000201 /* DataService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataService.swift; sourceTree = "<group>"; };
		0001000100010001001000211 /* AuthService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthService.swift; sourceTree = "<group>"; };
		0001000100010001001000213 /* PetService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PetService.swift; sourceTree = "<group>"; };
		0001000100010001001000215 /* FeedService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeedService.swift; sourceTree = "<group>"; };

		0001000100010001001000251 /* User.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = User.swift; sourceTree = "<group>"; };
		0001000100010001001000261 /* AIModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIModels.swift; sourceTree = "<group>"; };
		0001000100010001001000263 /* PetEnums.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PetEnums.swift; sourceTree = "<group>"; };

		0001000100010001001000160 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0001000100010001000000001 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0001000100010001000000010 = {
			isa = PBXGroup;
			children = (
				0001000100010001000000012 /* FurryKids */,
				0001000100010001000000011 /* Products */,
			);
			sourceTree = "<group>";
		};
		0001000100010001000000011 /* Products */ = {
			isa = PBXGroup;
			children = (
				0001000100010001000100001 /* FurryKids.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0001000100010001000000012 /* FurryKids */ = {
			isa = PBXGroup;
			children = (
				0001000100010001001000021 /* FurryKidsApp.swift */,
				0001000100010001001000011 /* ContentView.swift */,
				0001000100010001000000013 /* Models */,
				0001000100010001000000014 /* Views */,
				0001000100010001000000017 /* Stores */,
				0001000100010001000000018 /* Services */,
				0001000100010001000000015 /* Utilities */,
				0001000100010001001000111 /* Assets.xcassets */,
				0001000100010001001000160 /* Info.plist */,
				0001000100010001000000016 /* Preview Content */,
			);
			path = FurryKids;
			sourceTree = "<group>";
		};
		0001000100010001000000013 /* Models */ = {
			isa = PBXGroup;
			children = (
				0001000100010001001000031 /* Pet.swift */,
				0001000100010001001000041 /* Feed.swift */,
				0001000100010001001000051 /* Message.swift */,
				0001000100010001001000251 /* User.swift */,
				0001000100010001001000261 /* AIModels.swift */,
				0001000100010001001000263 /* PetEnums.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		0001000100010001000000014 /* Views */ = {
			isa = PBXGroup;
			children = (
				0001000100010001001000071 /* FeedView.swift */,
				0001000100010001001000081 /* ProfileView.swift */,
				0001000100010001001000091 /* InteractionView.swift */,
				0001000100010001001000101 /* CreateFeedView.swift */,
				0001000100010001001000201 /* AddPetView.swift */,
				0001000100010001001000211 /* PetDetailView.swift */,
				0001000100010001001000221 /* FeedCommentsView.swift */,
				0001000100010001001000226 /* EditFeedView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		0001000100010001000000015 /* Utilities */ = {
			isa = PBXGroup;
			children = (
				0001000100010001001000061 /* Color+Extensions.swift */,
				0001000100010001001000171 /* SpeechHelper.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		0001000100010001000000017 /* Stores */ = {
			isa = PBXGroup;
			children = (
				0001000100010001001000151 /* InteractionStore.swift */,
				0001000100010001001000153 /* AuthStore.swift */,
				0001000100010001001000155 /* PetStore.swift */,
				0001000100010001001000157 /* FeedStore.swift */,
			);
			path = Stores;
			sourceTree = "<group>";
		};
		0001000100010001000000018 /* Services */ = {
			isa = PBXGroup;
			children = (
				0001000100010001001000181 /* NetworkManager.swift */,
				0001000100010001001000191 /* AIService.swift */,
				0001000100010001001000201 /* DataService.swift */,
				0001000100010001001000211 /* AuthService.swift */,
				0001000100010001001000213 /* PetService.swift */,
				0001000100010001001000215 /* FeedService.swift */,
				0001000100010001001000231 /* CommentService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		0001000100010001000000016 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				0001000100010001001000121 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};

/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		0001000100010001000000100 /* FurryKids */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0001000100010001000000101 /* Build configuration list for PBXNativeTarget "FurryKids" */;
			buildPhases = (
				0001000100010001000000001 /* Frameworks */,
				0001000100010001000000002 /* Sources */,
				0001000100010001000000003 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = FurryKids;
			productName = FurryKids;
			productReference = 0001000100010001000100001 /* FurryKids.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0001000100010001000000200 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					0001000100010001000000100 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = 0001000100010001000000201 /* Build configuration list for PBXProject "FurryKids" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 0001000100010001000000010;
			productRefGroup = 0001000100010001000000011 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0001000100010001000000100 /* FurryKids */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0001000100010001000000003 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0001000100010001001000120 /* Preview Assets.xcassets in Resources */,
				0001000100010001001000110 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0001000100010001000000002 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0001000100010001001000010 /* ContentView.swift in Sources */,
				0001000100010001001000020 /* FurryKidsApp.swift in Sources */,
				0001000100010001001000030 /* Pet.swift in Sources */,
				0001000100010001001000040 /* Feed.swift in Sources */,
				0001000100010001001000050 /* Message.swift in Sources */,
				0001000100010001001000060 /* Color+Extensions.swift in Sources */,
				0001000100010001001000070 /* FeedView.swift in Sources */,
				0001000100010001001000080 /* ProfileView.swift in Sources */,
				0001000100010001001000090 /* InteractionView.swift in Sources */,
				0001000100010001001000100 /* CreateFeedView.swift in Sources */,
				0001000100010001001000200 /* AddPetView.swift in Sources */,
				0001000100010001001000210 /* PetDetailView.swift in Sources */,
				0001000100010001001000220 /* FeedCommentsView.swift in Sources */,
				0001000100010001001000225 /* EditFeedView.swift in Sources */,

				0001000100010001001000150 /* InteractionStore.swift in Sources */,
				0001000100010001001000152 /* AuthStore.swift in Sources */,
				0001000100010001001000154 /* PetStore.swift in Sources */,
				0001000100010001001000156 /* FeedStore.swift in Sources */,
				0001000100010001001000170 /* SpeechHelper.swift in Sources */,
				0001000100010001001000180 /* NetworkManager.swift in Sources */,
				0001000100010001001000190 /* AIService.swift in Sources */,
				0001000100010001001000200 /* DataService.swift in Sources */,
				0001000100010001001000210 /* AuthService.swift in Sources */,
				0001000100010001001000212 /* PetService.swift in Sources */,
				0001000100010001001000214 /* FeedService.swift in Sources */,
				0001000100010001001000230 /* CommentService.swift in Sources */,
				0001000100010001001000250 /* User.swift in Sources */,
				0001000100010001001000260 /* AIModels.swift in Sources */,
				0001000100010001001000262 /* PetEnums.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		0001000100010001000000301 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		0001000100010001000000302 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		0001000100010001000000401 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"FurryKids/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.FurryKids;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		0001000100010001000000402 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"FurryKids/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.FurryKids;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0001000100010001000000101 /* Build configuration list for PBXNativeTarget "FurryKids" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0001000100010001000000401 /* Debug */,
				0001000100010001000000402 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0001000100010001000000201 /* Build configuration list for PBXProject "FurryKids" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0001000100010001000000301 /* Debug */,
				0001000100010001000000302 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0001000100010001000000200 /* Project object */;
} 