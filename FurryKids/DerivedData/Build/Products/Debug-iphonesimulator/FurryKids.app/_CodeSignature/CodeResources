<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key><EMAIL></key>
		<data>
		nMAim2/XH1yL3ZPY0mrFL2w06dk=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		DsbEmOhcSHrfJU4Y8IelsHBPMdQ=
		</data>
		<key>Assets.car</key>
		<data>
		6rF9WvpFJVcZRaygkXwAzvKrNAE=
		</data>
		<key>FurryKids.debug.dylib</key>
		<data>
		wa2zHb+W58r8b4XFx2H6OjBdYl4=
		</data>
		<key>Info.plist</key>
		<data>
		6QSYkEF6f0YeGVrdf0AiCMLWdkY=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>__preview.dylib</key>
		<data>
		CBXMilFlZMwdoWtEtwT3QobIrGM=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			C91wqecAzKKeUW36FTZjbAXsLVIfDEDySpXGPIr8Mrs=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			9oO1e18U4i4r2kEZvWnvv5PWtTCwLQXxlAa2L8nhsew=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			ZP/FGeJHXH6V+L4SSKVutxgq1QtS74GccuQ+9Nmwq7I=
			</data>
		</dict>
		<key>FurryKids.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			P14Z5Jv45skzYrW2P5oN3kWmFdRd0I1SFsjs8LMNUm0=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			ATOUJcxoFvWEtgkhsQn9/OHAudkU8l8b/NuvnMHgsII=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
