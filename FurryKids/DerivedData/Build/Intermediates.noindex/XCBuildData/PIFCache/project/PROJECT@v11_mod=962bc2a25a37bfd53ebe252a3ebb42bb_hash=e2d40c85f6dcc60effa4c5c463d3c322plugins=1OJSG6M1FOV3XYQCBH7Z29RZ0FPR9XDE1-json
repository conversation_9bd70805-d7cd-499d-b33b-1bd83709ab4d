{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "e2d40c85f6dcc60effa4c5c463d3c3225aaa5fbf05eefb1dc2657df0542ad6a6", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "VALIDATE_PRODUCT": "YES"}, "guid": "e2d40c85f6dcc60effa4c5c463d3c32234766768fd959f62013ef78d61bdfc2c", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c3226bfa04e9336c8d2e267742f1a2487ccf", "path": "FurryKidsApp.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c3222bfc8d4f4ef9325c84ad53af99b26818", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c322fe8dbbbb6c93079646c384e511b4b0ad", "path": "<PERSON><PERSON>swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c3222cc094f002cba7d9b606c7c28a3be801", "path": "Feed.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c32292a3a9f30609479357cc0e143662ff78", "path": "Message.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c322ec02598ac5d8e10adc8c5a1d0f2ca66e", "path": "User.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c322f01480f03ef716f1f9b4342192b7a957", "path": "AIModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c3221df867c5a4c557b93e92204703c344fa", "path": "PetEnums.swift", "sourceTree": "<group>", "type": "file"}], "guid": "e2d40c85f6dcc60effa4c5c463d3c322ab36d481cb121d69bf60c4eb4bb2eb0e", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c322a9e4ce707dd99fc19eff4a427119dac9", "path": "FeedView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c322aa0a0b3923842617ca70c05a675cc130", "path": "ProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c3221d40639655cd483bfe0d5b2f2f09218c", "path": "InteractionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c3229d23bd268bd28fdd6cad75e329f8f53f", "path": "CreateFeedView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "e2d40c85f6dcc60effa4c5c463d3c322f3d1e4f1865f58a35b344459e2d16f4a", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c32243b8382f8b7da55e8792bfb78aee6472", "path": "InteractionStore.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c32212a6bb4d1d7b4fd5c12505d6478a2076", "path": "AuthStore.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c3223655303a43bfe4b4c7c3d5263ab4f77a", "path": "PetStore.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c322b2317a101d203323e2ec5ae119d20275", "path": "FeedStore.swift", "sourceTree": "<group>", "type": "file"}], "guid": "e2d40c85f6dcc60effa4c5c463d3c3226fede422e268303c894a668282734abe", "name": "Stores", "path": "Stores", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c32295d38c31c627c89992ee0340b6781da0", "path": "NetworkManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c322f4fd1e3f572ac25a602b5f4480b6539a", "path": "AIService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c322a506b64f4ade9577703dcd5842de732e", "path": "DataService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c322392fefb6aea8199748ad629a9fd4ea3a", "path": "AuthService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c322256b4635b9a113c7e3ce1960a7ef862d", "path": "PetService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c3227a78b45e9525d77a1f30f84fe88f6131", "path": "FeedService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "e2d40c85f6dcc60effa4c5c463d3c3226b9334cfaae400ce5cf32da7a7365a89", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c322bf58400dd6d38f0a87ee3a0b06f669bd", "path": "Color+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "e2d40c85f6dcc60effa4c5c463d3c3226cf7d9b00ac929b68471bd53bdb4ca0b", "path": "SpeechHelper.swift", "sourceTree": "<group>", "type": "file"}], "guid": "e2d40c85f6dcc60effa4c5c463d3c322caf0fc4b6aff1a2d9928e1e063de1973", "name": "Utilities", "path": "Utilities", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "e2d40c85f6dcc60effa4c5c463d3c3225c0181228cad68ad5deced9718a048e1", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "e2d40c85f6dcc60effa4c5c463d3c32219ac260d393437e71026edd8e0fa6e6a", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "e2d40c85f6dcc60effa4c5c463d3c322e02460e3a5d356870396f04bfc73e74d", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "e2d40c85f6dcc60effa4c5c463d3c32266f0ba335de3d65c773b04e990515b17", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}], "guid": "e2d40c85f6dcc60effa4c5c463d3c32288761a9670ffbfe76d98714201f28d0e", "name": "FurryKids", "path": "FurryKids", "sourceTree": "<group>", "type": "group"}, {"guid": "e2d40c85f6dcc60effa4c5c463d3c32241adfd644a9a70469908aa9bb9b16e13", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "e2d40c85f6dcc60effa4c5c463d3c322fd3a4237b01be6f2e6076619e63b9f84", "name": "FurryKids", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "e2d40c85f6dcc60effa4c5c463d3c322", "path": "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids.xcodeproj", "projectDirectory": "/Users/<USER>/WorkSpace/furryKids/FurryKids", "targets": ["TARGET@v11_hash=15aa8f6f55c758dda62e44e6cc27ebf6"]}