{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"FurryKids/Preview Content\"", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.example.FurryKids", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "e2d40c85f6dcc60effa4c5c463d3c322e4918c34e94371773b0a5eb2232c23fe", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"FurryKids/Preview Content\"", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.example.FurryKids", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "e2d40c85f6dcc60effa4c5c463d3c3220640dc1c802ff1566fa761d0dede2209", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "e2d40c85f6dcc60effa4c5c463d3c3226fb31668fbb2b8c365a14f434f30e3d6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "e2d40c85f6dcc60effa4c5c463d3c3222bfc8d4f4ef9325c84ad53af99b26818", "guid": "e2d40c85f6dcc60effa4c5c463d3c3227e5305b5919b2833196f781aca0b27b9"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c3226bfa04e9336c8d2e267742f1a2487ccf", "guid": "e2d40c85f6dcc60effa4c5c463d3c322d9cd5632c1c1dd486f799b57b52112a2"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322fe8dbbbb6c93079646c384e511b4b0ad", "guid": "e2d40c85f6dcc60effa4c5c463d3c3223d702c8e4b56ba74ab785f19340b08dc"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c3222cc094f002cba7d9b606c7c28a3be801", "guid": "e2d40c85f6dcc60effa4c5c463d3c3228c311d17fb6643faf43687249543c2f6"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c32292a3a9f30609479357cc0e143662ff78", "guid": "e2d40c85f6dcc60effa4c5c463d3c32247bafbfd933e2c0564779acac9a01844"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322bf58400dd6d38f0a87ee3a0b06f669bd", "guid": "e2d40c85f6dcc60effa4c5c463d3c3226954aab80dc4e06a612a479bc7de218d"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322a9e4ce707dd99fc19eff4a427119dac9", "guid": "e2d40c85f6dcc60effa4c5c463d3c322b3b35353c690982e8f3713d840fd64c1"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322aa0a0b3923842617ca70c05a675cc130", "guid": "e2d40c85f6dcc60effa4c5c463d3c32271b8c9bf0adf3ee8023d817530a190b2"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c3221d40639655cd483bfe0d5b2f2f09218c", "guid": "e2d40c85f6dcc60effa4c5c463d3c322cc75f685aae4869ec07cfcc54d3eeb2c"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c3229d23bd268bd28fdd6cad75e329f8f53f", "guid": "e2d40c85f6dcc60effa4c5c463d3c32243d4b41888d4103abef69e6b89eedc37"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322a506b64f4ade9577703dcd5842de732e", "guid": "e2d40c85f6dcc60effa4c5c463d3c322bd494f284479a64d550fd3c9450c119f"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322392fefb6aea8199748ad629a9fd4ea3a", "guid": "e2d40c85f6dcc60effa4c5c463d3c3222f04b4eb5643ee5f63a07ed9fd94163f"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c32243b8382f8b7da55e8792bfb78aee6472", "guid": "e2d40c85f6dcc60effa4c5c463d3c322f07627ac568ecb5e701ca32aae4c990e"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c32212a6bb4d1d7b4fd5c12505d6478a2076", "guid": "e2d40c85f6dcc60effa4c5c463d3c3229ed6a23aedf1ea8118f8337abf2a4cf1"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c3223655303a43bfe4b4c7c3d5263ab4f77a", "guid": "e2d40c85f6dcc60effa4c5c463d3c32245770b3c467e9e973b4cdb40c1c02d85"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322b2317a101d203323e2ec5ae119d20275", "guid": "e2d40c85f6dcc60effa4c5c463d3c32265742d02b2dd3ec1014cf4586482a25d"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c3226cf7d9b00ac929b68471bd53bdb4ca0b", "guid": "e2d40c85f6dcc60effa4c5c463d3c3220d074ee64c10aff837bf2db4302f85ee"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c32295d38c31c627c89992ee0340b6781da0", "guid": "e2d40c85f6dcc60effa4c5c463d3c32260518ff3234f6ca68651d6e60a4f1e0f"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322f4fd1e3f572ac25a602b5f4480b6539a", "guid": "e2d40c85f6dcc60effa4c5c463d3c3229f392bc354f9f456b015770dc36da686"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322a506b64f4ade9577703dcd5842de732e", "guid": "e2d40c85f6dcc60effa4c5c463d3c322bd494f284479a64d550fd3c9450c119f"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322392fefb6aea8199748ad629a9fd4ea3a", "guid": "e2d40c85f6dcc60effa4c5c463d3c3222f04b4eb5643ee5f63a07ed9fd94163f"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322256b4635b9a113c7e3ce1960a7ef862d", "guid": "e2d40c85f6dcc60effa4c5c463d3c32277f67084f6c289ce4937ac9223de7124"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c3227a78b45e9525d77a1f30f84fe88f6131", "guid": "e2d40c85f6dcc60effa4c5c463d3c32273faa1f1b860b230c0a82948519fffca"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322ec02598ac5d8e10adc8c5a1d0f2ca66e", "guid": "e2d40c85f6dcc60effa4c5c463d3c322904000e13b1d02c600fc9526e43bae05"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322f01480f03ef716f1f9b4342192b7a957", "guid": "e2d40c85f6dcc60effa4c5c463d3c32269c95c31edc762232c82b2be8ff16836"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c3221df867c5a4c557b93e92204703c344fa", "guid": "e2d40c85f6dcc60effa4c5c463d3c322ccd4721ab100be5f0e0622c701c2a45e"}], "guid": "e2d40c85f6dcc60effa4c5c463d3c3226de56b433c7ea55b148b031db1783aad", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "e2d40c85f6dcc60effa4c5c463d3c322e02460e3a5d356870396f04bfc73e74d", "guid": "e2d40c85f6dcc60effa4c5c463d3c322df41a6e47d16f79e7cf7430c91c375ec"}, {"fileReference": "e2d40c85f6dcc60effa4c5c463d3c3225c0181228cad68ad5deced9718a048e1", "guid": "e2d40c85f6dcc60effa4c5c463d3c32290286554859cdc6af30d09f02ac21244"}], "guid": "e2d40c85f6dcc60effa4c5c463d3c32207c5540e56d028dbae9adf5f081a35b6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "e2d40c85f6dcc60effa4c5c463d3c322e0bd1a58b8667f74c5b4c67ede0d85f5", "name": "FurryKids", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "e2d40c85f6dcc60effa4c5c463d3c32221135f095a8ac16bfb771faf8af97737", "name": "FurryKids.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}