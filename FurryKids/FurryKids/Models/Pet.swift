import Foundation

// MARK: - Pet数据模型 (匹配后端API)
struct Pet: Codable, Identifiable {
    let id: Int
    let name: String
    let breed: String
    let age: Int?
    let ageDisplay: String
    let gender: String
    let color: String?
    let size: String
    let weight: Double?
    let avatarUrl: String?
    let personality: String?
    let personalityTags: [String]
    let currentMood: String
    let moodDescription: String?
    let responseStyle: String
    let interactionCount: Int
    let experiencePoints: Int
    let level: Int
    let lastInteractionAt: Date?
    let ownerId: Int
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date

    // 前端显示用的计算属性
    var personalityTagsArray: [String] {
        return personalityTags
    }

    // 兼容性属性
    var avatar: String? {
        return avatarUrl
    }

    var mood: String {
        return currentMood
    }

    var experience: Int {
        return experiencePoints
    }

    var maxExperience: Int {
        // 根据等级计算最大经验值
        return level * 100
    }

    var status: String {
        return isActive ? "在线" : "离线"
    }

    var signature: String {
        return moodDescription ?? "这是一只可爱的\(breed)！"
    }

    // 编码键映射
    enum CodingKeys: String, CodingKey {
        case id, name, breed, age, gender, color, size, weight, personality, level
        case ageDisplay = "age_display"
        case avatarUrl = "avatar_url"
        case personalityTags = "personality_tags"
        case currentMood = "current_mood"
        case moodDescription = "mood_description"
        case responseStyle = "response_style"
        case interactionCount = "interaction_count"
        case experiencePoints = "experience_points"
        case lastInteractionAt = "last_interaction_at"
        case ownerId = "owner_id"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Pet创建/更新请求模型
struct PetCreateRequest: Codable {
    let name: String
    let type: String
    let breed: String?
    let personality: [String]  // 改为数组类型
    let avatar: String?
    let birthday: Date?
    let weight: Double?
    let healthStatus: String

    // 前端特有的属性
    let signature: String?
    let mood: String?
    let status: String?
    let level: Int?
    let experience: Int?
    let maxExperience: Int?
    let moodDescription: String?
    let aiPersonalityPrompt: String?

    enum CodingKeys: String, CodingKey {
        case name, type, breed, personality, avatar, birthday, weight
        case signature, mood, status, level, experience, maxExperience
        case healthStatus = "health_status"
        case moodDescription = "mood_description"
        case aiPersonalityPrompt = "ai_personality_prompt"
    }
}

// MARK: - 后端API兼容的创建请求模型
struct BackendPetCreateRequest: Codable {
    let name: String
    let breed: String
    let age: Int?
    let gender: String
    let color: String?
    let size: String
    let weight: Double?
    let personality: String?
    let personalityTags: [String]
    let currentMood: String
    let responseStyle: String
    let avatarUrl: String?  // 添加头像字段

    enum CodingKeys: String, CodingKey {
        case name, breed, age, gender, color, size, weight, personality
        case personalityTags = "personality_tags"
        case currentMood = "current_mood"
        case responseStyle = "response_style"
        case avatarUrl = "avatar_url"
    }
}

struct PetUpdateRequest: Codable {
    let name: String?
    let type: String?
    let breed: String?
    let personality: [String]?  // 改为数组类型
    let avatar: String?
    let birthday: Date?
    let weight: Double?
    let healthStatus: String?

    // 前端特有的属性
    let signature: String?
    let mood: String?
    let status: String?
    let level: Int?
    let experience: Int?
    let maxExperience: Int?
    let moodDescription: String?
    let aiPersonalityPrompt: String?

    enum CodingKeys: String, CodingKey {
        case name, type, breed, personality, avatar, birthday, weight
        case signature, mood, status, level, experience, maxExperience
        case healthStatus = "health_status"
        case moodDescription = "mood_description"
        case aiPersonalityPrompt = "ai_personality_prompt"
    }
}

// 示例数据
extension Pet {
    static let sample = Pet(
        id: 1,
        name: "Buddy",
        breed: "柴犬",
        age: 24,
        ageDisplay: "2岁",
        gender: "UNKNOWN",
        color: "金黄色",
        size: "MEDIUM",
        weight: 25.5,
        avatarUrl: "🐶",
        personality: "活泼好动，喜欢玩耍",
        personalityTags: ["活泼", "好奇", "友善"],
        currentMood: "HAPPY",
        moodDescription: "我是一只可爱的柴犬，喜欢玩球和晒太阳！",
        responseStyle: "friendly",
        interactionCount: 10,
        experiencePoints: 75,
        level: 5,
        lastInteractionAt: Date().addingTimeInterval(-3600),
        ownerId: 1,
        isActive: true,
        createdAt: Date().addingTimeInterval(-86400 * 30),
        updatedAt: Date()
    )

    static let sampleData = [
        Pet(
            id: 2,
            name: "小毛球",
            breed: "金毛",
            age: 24,
            ageDisplay: "2岁",
            gender: "UNKNOWN",
            color: "金黄色",
            size: "LARGE",
            weight: 25.5,
            avatarUrl: "🐕",
            personality: "活泼粘人，聪明可爱",
            personalityTags: ["活泼", "粘人", "聪明"],
            currentMood: "HAPPY",
            moodDescription: "我是一只活泼的金毛，喜欢和主人一起玩耍！",
            responseStyle: "friendly",
            interactionCount: 8,
            experiencePoints: 45,
            level: 3,
            lastInteractionAt: Date().addingTimeInterval(-1800),
            ownerId: 1,
            isActive: true,
            createdAt: Date().addingTimeInterval(-86400 * 30),
            updatedAt: Date()
        ),

        Pet(
            id: 3,
            name: "小橘猫",
            breed: "橘猫",
            age: 12,
            ageDisplay: "1岁",
            gender: "UNKNOWN",
            color: "橘色",
            size: "SMALL",
            weight: 4.2,
            avatarUrl: "🐱",
            personality: "慵懒独立，喜欢晒太阳",
            personalityTags: ["慵懒", "好奇", "独立"],
            currentMood: "SLEEPY",
            moodDescription: "窗边的阳光是我最爱的地方~",
            responseStyle: "lazy",
            interactionCount: 5,
            experiencePoints: 30,
            level: 2,
            lastInteractionAt: Date().addingTimeInterval(-7200),
            ownerId: 1,
            isActive: false,
            createdAt: Date().addingTimeInterval(-86400 * 20),
            updatedAt: Date()
        ),

        Pet(
            id: 4,
            name: "旺财",
            breed: "德牧",
            age: 36,
            ageDisplay: "3岁",
            gender: "MALE",
            color: "黑棕色",
            size: "LARGE",
            weight: 35.0,
            avatarUrl: "🐕‍🦺",
            personality: "忠诚勇敢，警觉性高",
            personalityTags: ["忠诚", "警觉", "勇敢"],
            currentMood: "ALERT",
            moodDescription: "保护主人是我的责任！",
            responseStyle: "protective",
            interactionCount: 15,
            experiencePoints: 60,
            level: 4,
            lastInteractionAt: Date().addingTimeInterval(-900),
            ownerId: 1,
            isActive: true,
            createdAt: Date().addingTimeInterval(-86400 * 60),
            updatedAt: Date()
        )
    ]
}