import Foundation

// MARK: - 用户数据模型
struct User: Codable, Identifiable {
    let id: Int
    let username: String
    let provider: String // "local", "google", "facebook"
    let email: String?
    let displayName: String?
    let avatar: String?
    let petCount: Int?
    let feedCount: Int?
    let chatCount: Int?
    let createdAt: String?
    let updatedAt: String?

    var isLoggedIn: Bool {
        return id > 0
    }
}

// MARK: - 认证请求模型
struct LoginRequest: Codable {
    let username: String
    let password: String
}

struct RegisterRequest: Codable {
    let username: String
    let password: String
    let email: String?
}

// MARK: - 认证响应模型
struct AuthResponse: Codable {
    let success: Bool
    let message: String
    let user: User?
    let token: String?  // JWT token
    let refreshToken: String?  // 刷新token
    let accessToken: String?  // 后端返回的access_token字段

    // 自定义解码，处理后端返回的access_token字段
    enum CodingKeys: String, CodingKey {
        case success, message, user, token, refreshToken
        case accessToken = "access_token"
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        success = try container.decode(Bool.self, forKey: .success)
        message = try container.decode(String.self, forKey: .message)
        user = try container.decodeIfPresent(User.self, forKey: .user)

        // 优先使用token字段，如果没有则使用access_token字段
        if let tokenValue = try container.decodeIfPresent(String.self, forKey: .token) {
            token = tokenValue
        } else {
            token = try container.decodeIfPresent(String.self, forKey: .accessToken)
        }

        refreshToken = try container.decodeIfPresent(String.self, forKey: .refreshToken)
        accessToken = try container.decodeIfPresent(String.self, forKey: .accessToken)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(success, forKey: .success)
        try container.encode(message, forKey: .message)
        try container.encodeIfPresent(user, forKey: .user)
        try container.encodeIfPresent(token, forKey: .token)
        try container.encodeIfPresent(refreshToken, forKey: .refreshToken)
        try container.encodeIfPresent(accessToken, forKey: .accessToken)
    }


}

struct UserInfoResponse: Codable {
    let user: User?
    let authType: String?
}

struct ErrorResponse: Codable {
    let success: Bool
    let message: String
    let code: String?
} 