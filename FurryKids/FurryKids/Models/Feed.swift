import Foundation

// MARK: - Feed数据模型 (匹配backend API)
struct Feed: Codable, Identifiable {
    let id: Int
    let petId: Int
    let userId: Int
    let content: String
    let images: [String]
    let tags: [String]
    let mood: String?
    let location: String?
    let isPublic: Bool      // 是否公开
    let status: String      // "published", "draft", "hidden", "deleted"
    var likesCount: Int
    var commentsCount: Int
    var sharesCount: Int
    let viewsCount: Int
    var isLiked: Bool
    let createdAt: Date
    let updatedAt: Date?    // 修改为可选类型，因为后端可能返回null

    // 后端返回的额外字段
    let aiGeneratedContent: String?
    let aiMoodScore: Double?
    let aiQualityScore: Double?
    let aiTags: [String]?
    let isFeatured: Bool
    let isAiGenerated: Bool
    let publishedAt: Date?
    let petName: String?
    let petAvatar: String?
    let userName: String?

    // 关联数据 (从API获取时可能包含)
    let pet: Pet?

    // 编码键映射
    enum CodingKeys: String, CodingKey {
        case id, content, images, tags, mood, location, status, pet
        case isPublic = "is_public"
        case petId = "pet_id"
        case userId = "user_id"
        case likesCount = "likes_count"
        case commentsCount = "comments_count"
        case sharesCount = "shares_count"
        case viewsCount = "views_count"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case isLiked = "is_liked"
        case aiGeneratedContent = "ai_generated_content"
        case aiMoodScore = "ai_mood_score"
        case aiQualityScore = "ai_quality_score"
        case aiTags = "ai_tags"
        case isFeatured = "is_featured"
        case isAiGenerated = "is_ai_generated"
        case publishedAt = "published_at"
        case petName = "pet_name"
        case petAvatar = "pet_avatar"
        case userName = "user_name"
    }
}

// MARK: - Feed创建/更新请求模型
struct FeedCreateRequest: Codable {
    let petId: Int
    let content: String
    let images: [String]
    let tags: [String]
    let mood: String?
    let location: String?
    let isPublic: Bool

    enum CodingKeys: String, CodingKey {
        case content, images, tags, mood, location
        case isPublic = "is_public"
        case petId = "pet_id"
    }
}

struct FeedUpdateRequest: Codable {
    let content: String?
    let images: [String]?
    let tags: [String]?
    let mood: String?
    let location: String?
    let isPublic: Bool?
    let status: String?
}

// MARK: - Feed互动模型
struct FeedLike: Codable {
    let id: Int
    let feedId: Int
    let userId: Int
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, createdAt
        case feedId = "feed_id"
        case userId = "user_id"
    }
}

struct FeedComment: Codable, Identifiable {
    let id: Int
    let feedId: Int
    let userId: Int
    let content: String
    let parentId: Int?
    let isDeleted: Bool
    let isHidden: Bool
    let createdAt: Date
    let updatedAt: Date?  // 修改为可选类型，因为后端可能返回null

    // 关联数据
    let user: CommentUser?
    let userName: String?
    let userAvatar: String?
    let isReply: Bool
    let replyCount: Int
    let replies: [FeedComment]?

    enum CodingKeys: String, CodingKey {
        case id, content, replies, user
        case feedId = "feed_id"
        case userId = "user_id"
        case parentId = "parent_id"
        case isDeleted = "is_deleted"
        case isHidden = "is_hidden"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case userName = "user_name"
        case userAvatar = "user_avatar"
        case isReply = "is_reply"
        case replyCount = "reply_count"
    }
}

// MARK: - CommentUser Model
struct CommentUser: Codable {
    let id: Int
    let username: String
    let nickname: String?
    let avatar: String?

    enum CodingKeys: String, CodingKey {
        case id
        case username
        case nickname
        case avatar
    }
}

// MARK: - API Request Models
struct FeedCommentCreate: Codable {
    let content: String
    let parentId: Int?

    enum CodingKeys: String, CodingKey {
        case content
        case parentId = "parent_id"
    }
}

// MARK: - Feed评论列表响应
struct FeedCommentsResponse: Codable {
    let comments: [FeedComment]
    let total: Int
    let page: Int
    let size: Int
    let hasMore: Bool

    enum CodingKeys: String, CodingKey {
        case comments, total, page, size
        case hasMore = "has_more"
    }
}

// 示例数据
extension Feed {
    static let sampleData = [
        Feed(
            id: 1,
            petId: 1,
            userId: 1,
            content: "今天和主人去公园玩了，好开心！🌳🏃‍♂️",
            images: ["park_image1"],
            tags: ["户外活动", "遛狗"],
            mood: "开心",
            location: "中央公园",
            isPublic: true,
            status: "published",
            likesCount: 42,
            commentsCount: 12,
            sharesCount: 3,
            viewsCount: 156,
            isLiked: false,
            createdAt: Date().addingTimeInterval(-3600),
            updatedAt: Date().addingTimeInterval(-3600),
            aiGeneratedContent: nil,
            aiMoodScore: nil,
            aiQualityScore: nil,
            aiTags: nil,
            isFeatured: false,
            isAiGenerated: false,
            publishedAt: Date().addingTimeInterval(-3600),
            petName: "小白",
            petAvatar: "🐕",
            userName: "主人",
            pet: Pet.sampleData.first
        ),
        Feed(
            id: 2,
            petId: 2,
            userId: 1,
            content: "窗边晒太阳真舒服，这是我的最爱！☀️😌",
            images: ["sunbath_image"],
            tags: ["晒太阳", "休闲"],
            mood: "慵懒",
            location: nil,
            isPublic: true,
            status: "published",
            likesCount: 35,
            commentsCount: 8,
            sharesCount: 1,
            viewsCount: 89,
            isLiked: true,
            createdAt: Date().addingTimeInterval(-7200),
            updatedAt: Date().addingTimeInterval(-7200),
            aiGeneratedContent: nil,
            aiMoodScore: nil,
            aiQualityScore: nil,
            aiTags: nil,
            isFeatured: false,
            isAiGenerated: false,
            publishedAt: Date().addingTimeInterval(-7200),
            petName: "小毛球",
            petAvatar: "🐱",
            userName: "主人",
            pet: Pet.sampleData.count > 1 ? Pet.sampleData[1] : nil
        ),
        Feed(
            id: 3,
            petId: 3,
            userId: 1,
            content: "刚刚吃了超级好吃的胡萝卜！🥕 我的最爱！",
            images: ["carrot_image"],
            tags: ["美食", "零食"],
            mood: "满足",
            location: nil,
            isPublic: true,
            status: "published",
            likesCount: 28,
            commentsCount: 5,
            sharesCount: 0,
            viewsCount: 67,
            isLiked: false,
            createdAt: Date().addingTimeInterval(-10800),
            updatedAt: Date().addingTimeInterval(-10800),
            aiGeneratedContent: nil,
            aiMoodScore: nil,
            aiQualityScore: nil,
            aiTags: nil,
            isFeatured: false,
            isAiGenerated: false,
            publishedAt: Date().addingTimeInterval(-10800),
            petName: "小兔子",
            petAvatar: "🐰",
            userName: "主人",
            pet: Pet.sampleData.count > 2 ? Pet.sampleData[2] : nil
        )
    ]
} 