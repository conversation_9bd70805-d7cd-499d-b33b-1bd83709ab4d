import Foundation
import Combine

class CommentService: ObservableObject {
    private let networkManager = NetworkManager.shared

    // MARK: - 获取评论列表
    func getComments(feedId: Int, page: Int = 1, size: Int = 20) -> AnyPublisher<FeedCommentsResponse, Error> {
        let endpoint = "/api/feeds/\(feedId)/comments"

        var headers: [String: String] = [:]
        if let token = APIConfig.authToken {
            headers["Authorization"] = "Bearer \(token)"
        }

        let queryParams = [
            "page": "\(page)",
            "size": "\(size)"
        ]

        let endpointWithQuery = endpoint + "?" + queryParams.map { "\($0.key)=\($0.value)" }.joined(separator: "&")

        return networkManager.request(
            endpoint: endpointWithQuery,
            method: .GET,
            headers: headers,
            responseType: FeedCommentsResponse.self
        )
        .mapError { $0 as Error }
        .eraseToAnyPublisher()
    }
    
    // MARK: - 创建评论
    func createComment(feedId: Int, content: String, parentId: Int? = nil) -> AnyPublisher<FeedComment, Error> {
        let endpoint = "/api/feeds/\(feedId)/comments"

        let commentData = FeedCommentCreate(content: content, parentId: parentId)

        var headers: [String: String] = ["Content-Type": "application/json"]
        if let token = APIConfig.authToken {
            headers["Authorization"] = "Bearer \(token)"
        }

        do {
            let bodyData = try JSONEncoder().encode(commentData)

            return networkManager.request(
                endpoint: endpoint,
                method: .POST,
                body: bodyData,
                headers: headers,
                responseType: FeedComment.self
            )
            .mapError { $0 as Error }
            .eraseToAnyPublisher()
        } catch {
            return Fail(error: error)
                .eraseToAnyPublisher()
        }
    }
    
    // MARK: - 删除评论
    func deleteComment(commentId: Int) -> AnyPublisher<Bool, Error> {
        let endpoint = "/api/feeds/comments/\(commentId)"

        var headers: [String: String] = [:]
        if let token = APIConfig.authToken {
            headers["Authorization"] = "Bearer \(token)"
        }

        return networkManager.request(
            endpoint: endpoint,
            method: .DELETE,
            headers: headers,
            responseType: EmptyResponse.self
        )
        .map { _ in true }
        .catch { _ in Just(false).setFailureType(to: Error.self) }
        .eraseToAnyPublisher()
    }
}
