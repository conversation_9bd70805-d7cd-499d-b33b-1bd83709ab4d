import Foundation
import Combine

class CommentService: ObservableObject {
    private let networkManager = NetworkManager.shared
    
    // MARK: - 获取评论列表
    func getComments(feedId: Int, page: Int = 1, size: Int = 20) -> AnyPublisher<FeedCommentsResponse, Error> {
        let url = "\(networkManager.baseURL)/api/feeds/\(feedId)/comments"
        
        var components = URLComponents(string: url)!
        components.queryItems = [
            URLQueryItem(name: "page", value: "\(page)"),
            URLQueryItem(name: "size", value: "\(size)")
        ]
        
        guard let finalURL = components.url else {
            return Fail(error: NetworkError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        var request = URLRequest(url: finalURL)
        request.httpMethod = "GET"
        
        // 添加认证头
        if let token = AuthService.shared.accessToken {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        return networkManager.performRequest(request, responseType: FeedCommentsResponse.self)
    }
    
    // MARK: - 创建评论
    func createComment(feedId: Int, content: String, parentId: Int? = nil) -> AnyPublisher<FeedComment, Error> {
        let url = "\(networkManager.baseURL)/api/feeds/\(feedId)/comments"
        
        guard let finalURL = URL(string: url) else {
            return Fail(error: NetworkError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        let commentData = FeedCommentCreate(content: content, parentId: parentId)
        
        var request = URLRequest(url: finalURL)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加认证头
        if let token = AuthService.shared.accessToken {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        do {
            request.httpBody = try JSONEncoder().encode(commentData)
        } catch {
            return Fail(error: error)
                .eraseToAnyPublisher()
        }
        
        return networkManager.performRequest(request, responseType: FeedComment.self)
    }
    
    // MARK: - 删除评论
    func deleteComment(commentId: Int) -> AnyPublisher<Bool, Error> {
        let url = "\(networkManager.baseURL)/api/feeds/comments/\(commentId)"
        
        guard let finalURL = URL(string: url) else {
            return Fail(error: NetworkError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        var request = URLRequest(url: finalURL)
        request.httpMethod = "DELETE"
        
        // 添加认证头
        if let token = AuthService.shared.accessToken {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        return URLSession.shared.dataTaskPublisher(for: request)
            .map { data, response in
                if let httpResponse = response as? HTTPURLResponse {
                    return httpResponse.statusCode == 200
                }
                return false
            }
            .mapError { error in
                return error as Error
            }
            .eraseToAnyPublisher()
    }
}

// MARK: - Network Errors
enum NetworkError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingError
    case serverError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "没有数据"
        case .decodingError:
            return "数据解析失败"
        case .serverError(let message):
            return "服务器错误: \(message)"
        }
    }
}
