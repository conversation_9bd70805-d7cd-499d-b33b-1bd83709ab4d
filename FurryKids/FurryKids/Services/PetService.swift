import Foundation
import Combine

// MARK: - Helper Response Types (EmptyResponse已在FeedService中定义)
struct PetListResponse: Codable {
    let pets: [Pet]
    let total: Int?
    let page: Int?
    let size: Int?
}

// MARK: - Pet Service Protocol
protocol PetServiceProtocol {
    // Pet CRUD
    func getUserPets(userId: Int) -> AnyPublisher<[Pet], APIError>
    func getPet(petId: Int) -> AnyPublisher<Pet, APIError>
    func createPet(_ request: BackendPetCreateRequest) -> AnyPublisher<Pet, APIError>
    func updatePet(petId: Int, request: PetUpdateRequest) -> AnyPublisher<Pet, APIError>
    func deletePet(petId: Int) -> AnyPublisher<Bool, APIError>

    // Pet Stats
    func getPetStats(petId: Int) -> AnyPublisher<PetStats, APIError>
    func updatePetActivity(petId: Int, activity: PetActivity) -> AnyPublisher<Bool, APIError>
}

// MARK: - Pet Activity Models
struct PetActivity: Codable {
    let type: String  // "feed", "walk", "play", "sleep"
    let timestamp: Date
    let notes: String?
}

struct PetStats: Codable {
    let totalFeeds: Int
    let totalLikes: Int
    let totalComments: Int
    let totalChatMessages: Int
    let lastActiveDate: Date?
    let moodStats: [String: Int]
    
    enum CodingKeys: String, CodingKey {
        case totalFeeds = "total_feeds"
        case totalLikes = "total_likes"
        case totalComments = "total_comments"
        case totalChatMessages = "total_chat_messages"
        case lastActiveDate = "last_active_date"
        case moodStats = "mood_stats"
    }
}

// MARK: - Pet Service Implementation
class PetService: PetServiceProtocol {
    static let shared = PetService()
    
    private let networkManager = NetworkManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // Local cache
    @Published var userPets: [Pet] = []
    @Published var currentPet: Pet?
    @Published var isLoading = false
    
    private init() {}
    
    // MARK: - Pet CRUD Operations
    func getUserPets(userId: Int) -> AnyPublisher<[Pet], APIError> {
        isLoading = true

        return networkManager.request(
            endpoint: "/api/pets/?page=1&size=50",
            method: .GET,
            responseType: PetListResponse.self
        )
        .map { response in
            return response.pets
        }
        .handleEvents(
            receiveOutput: { [weak self] pets in
                self?.userPets = pets
                if self?.currentPet == nil && !pets.isEmpty {
                    self?.currentPet = pets.first
                }
                self?.isLoading = false
            },
            receiveCompletion: { [weak self] _ in
                self?.isLoading = false
            }
        )
        .eraseToAnyPublisher()
    }
    
    func getPet(petId: Int) -> AnyPublisher<Pet, APIError> {
        return networkManager.request(
            endpoint: "/api/pets/\(petId)/",
            method: .GET,
            responseType: Pet.self
        )
        .handleEvents(receiveOutput: { [weak self] pet in
            // 更新缓存中的宠物信息
            if let index = self?.userPets.firstIndex(where: { $0.id == petId }) {
                self?.userPets[index] = pet
            }

            // 如果是当前宠物，更新当前宠物信息
            if self?.currentPet?.id == petId {
                self?.currentPet = pet
            }
        })
        .eraseToAnyPublisher()
    }
    
    func createPet(_ request: BackendPetCreateRequest) -> AnyPublisher<Pet, APIError> {
        guard let requestData = try? JSONEncoder().encode(request) else {
            print("❌ PetService: JSON编码失败")
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }

        print("📡 PetService: 发送创建宠物请求到 /api/pets/")
        print("📋 PetService: 请求体大小: \(requestData.count) bytes")

        return networkManager.request(
            endpoint: "/api/pets/",
            method: .POST,
            body: requestData,
            responseType: Pet.self
        )
        .handleEvents(
            receiveSubscription: { _ in
                print("🔄 PetService: 开始网络请求")
            },
            receiveOutput: { [weak self] newPet in
                print("✅ PetService: 收到响应 - 宠物ID: \(newPet.id), 名称: \(newPet.name)")
                self?.userPets.append(newPet)

                // 如果这是第一只宠物，设为当前宠物
                if self?.currentPet == nil {
                    self?.currentPet = newPet
                    print("🎯 PetService: 设置当前宠物为: \(newPet.name)")
                }
            },
            receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    print("❌ PetService: 网络请求失败 - \(error)")
                }
            }
        )
        .eraseToAnyPublisher()
    }
    
    func updatePet(petId: Int, request: PetUpdateRequest) -> AnyPublisher<Pet, APIError> {
        guard let requestData = try? JSONEncoder().encode(request) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }

        return networkManager.request(
            endpoint: "/api/pets/\(petId)/",
            method: .PUT,
            body: requestData,
            responseType: Pet.self
        )
        .handleEvents(receiveOutput: { [weak self] updatedPet in
            // 更新缓存中的宠物信息
            if let index = self?.userPets.firstIndex(where: { $0.id == petId }) {
                self?.userPets[index] = updatedPet
            }

            // 如果是当前宠物，更新当前宠物信息
            if self?.currentPet?.id == petId {
                self?.currentPet = updatedPet
            }
        })
        .eraseToAnyPublisher()
    }
    
    func deletePet(petId: Int) -> AnyPublisher<Bool, APIError> {
        return networkManager.request(
            endpoint: "/api/pets/\(petId)/",
            method: .DELETE,
            responseType: EmptyResponse.self
        )
        .map { _ in
            return true
        }
        .handleEvents(receiveOutput: { [weak self] success in
            if success {
                self?.userPets.removeAll { $0.id == petId }

                // 如果删除的是当前宠物，选择新的当前宠物
                if self?.currentPet?.id == petId {
                    self?.currentPet = self?.userPets.first
                }
            }
        })
        .eraseToAnyPublisher()
    }
    
    // MARK: - Pet Stats
    func getPetStats(petId: Int) -> AnyPublisher<PetStats, APIError> {
        return networkManager.request(
            endpoint: "/api/pets/\(petId)/stats/",
            method: .GET,
            responseType: PetStats.self
        )
        .eraseToAnyPublisher()
    }

    func updatePetActivity(petId: Int, activity: PetActivity) -> AnyPublisher<Bool, APIError> {
        guard let requestData = try? JSONEncoder().encode(activity) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }

        return networkManager.request(
            endpoint: "/api/pets/\(petId)/interaction/",
            method: .POST,
            body: requestData,
            responseType: Pet.self
        )
        .map { _ in
            return true
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Convenience Methods
    func setCurrentPet(_ pet: Pet) {
        currentPet = pet
    }
    
    func getCurrentPet() -> Pet? {
        return currentPet
    }
    
    func refreshUserPets() -> AnyPublisher<[Pet], APIError> {
        guard let currentUser = AuthService.shared.currentUser else {
            return Fail(error: APIError.unauthorized)
                .eraseToAnyPublisher()
        }
        
        return getUserPets(userId: currentUser.id)
    }
    
    // MARK: - Cache Management
    func clearCache() {
        userPets.removeAll()
        currentPet = nil
    }
    
    // MARK: - Pet Care Actions
    func feedPet(petId: Int, notes: String? = nil) -> AnyPublisher<Bool, APIError> {
        let activity = PetActivity(
            type: "feed",
            timestamp: Date(),
            notes: notes
        )
        
        return updatePetActivity(petId: petId, activity: activity)
            .handleEvents(receiveOutput: { [weak self] success in
                if success {
                    // 喂食成功，可以在这里更新宠物的其他状态
                    // 注意：新的Pet模型不再有lastFeedTime属性
                    print("🍖 宠物喂食成功")

                    if self?.currentPet?.id == petId {
                        // 当前宠物喂食成功
                        print("🎯 当前宠物喂食完成")
                    }
                }
            })
            .eraseToAnyPublisher()
    }
    
    func walkPet(petId: Int, notes: String? = nil) -> AnyPublisher<Bool, APIError> {
        let activity = PetActivity(
            type: "walk",
            timestamp: Date(),
            notes: notes
        )
        
        return updatePetActivity(petId: petId, activity: activity)
            .handleEvents(receiveOutput: { [weak self] success in
                if success {
                    // 散步成功，可以在这里更新宠物的其他状态
                    // 注意：新的Pet模型不再有lastWalkTime属性
                    print("🚶‍♂️ 宠物散步成功")

                    if self?.currentPet?.id == petId {
                        // 当前宠物散步成功
                        print("🎯 当前宠物散步完成")
                    }
                }
            })
            .eraseToAnyPublisher()
    }
    
    func playWithPet(petId: Int, notes: String? = nil) -> AnyPublisher<Bool, APIError> {
        let activity = PetActivity(
            type: "play",
            timestamp: Date(),
            notes: notes
        )
        
        return updatePetActivity(petId: petId, activity: activity)
    }
}
