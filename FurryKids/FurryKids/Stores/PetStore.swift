import Foundation
import Combine

// MARK: - PetStore (集成后端API)
@MainActor
class PetStore: ObservableObject {
    @Published var pets: [Pet] = []
    @Published var currentPet: Pet?
    @Published var isLoading = false
    @Published var error: String?
    
    private let petService = PetService.shared
    private let authService = AuthService.shared
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        // 初始加载用户宠物
        loadUserPets()
    }
    
    // MARK: - 加载用户宠物列表
    func loadUserPets() {
        guard let currentUser = authService.currentUser else {
            print("❌ PetStore.loadUserPets: 用户未登录")
            error = "用户未登录"
            return
        }

        print("🐾 PetStore.loadUserPets 开始加载用户宠物列表，用户ID: \(currentUser.id)")
        isLoading = true
        error = nil

        petService.getUserPets(userId: currentUser.id)
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false

                        if case .failure(let error) = completion {
                            print("❌ PetStore.loadUserPets 加载失败: \(error)")
                            print("🔍 PetStore.loadUserPets: 错误详情 - \(error.localizedDescription)")

                            // 详细的错误分析
                            switch error {
                            case .decodingError:
                                print("🔍 PetStore.loadUserPets: JSON解析错误 - 可能是数据格式不匹配")
                            case .unauthorized:
                                print("🔍 PetStore.loadUserPets: 认证错误 - token可能无效")
                            case .networkError(let underlyingError):
                                print("🔍 PetStore.loadUserPets: 网络错误 - \(underlyingError)")
                            case .serverError(let code, let message):
                                print("🔍 PetStore.loadUserPets: 服务器错误 - 状态码: \(code), 消息: \(message ?? "无")")
                            default:
                                print("🔍 PetStore.loadUserPets: 其他错误 - \(error)")
                            }

                            let errorMessage = self?.handleAPIError(error) ?? "未知错误"
                            print("❌ 加载用户宠物列表失败: \(errorMessage)")
                            self?.error = errorMessage
                        }
                    }
                },
                receiveValue: { [weak self] pets in
                    DispatchQueue.main.async {
                        print("✅ 成功加载用户宠物列表，数量: \(pets.count)")
                        for pet in pets {
                            print("🐕 宠物: ID=\(pet.id), 名称=\(pet.name), 品种=\(pet.breed)")
                        }

                        self?.pets = pets

                        // 如果没有当前宠物，设置第一个为当前宠物
                        if self?.currentPet == nil && !pets.isEmpty {
                            self?.currentPet = pets.first
                            print("🎯 设置当前宠物: \(pets.first?.name ?? "未知")")
                        } else if let currentPet = self?.currentPet {
                            print("🎯 当前宠物: \(currentPet.name)")
                        } else {
                            print("⚠️ 没有宠物可用")
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 创建宠物
    func createPet(name: String, breed: String, age: Int? = nil, gender: PetGender = .unknown,
                   color: String? = nil, size: PetSize = .medium, weight: Double? = nil,
                   personality: String? = nil, personalityTags: [String] = [],
                   currentMood: PetMood = .happy, responseStyle: String = "friendly") {
        isLoading = true
        error = nil

        // 创建符合后端API的请求
        let request = BackendPetCreateRequest(
            name: name,
            breed: breed,
            age: age,
            gender: gender.rawValue,
            color: color,
            size: size.rawValue,
            weight: weight,
            personality: personality,
            personalityTags: personalityTags,
            currentMood: currentMood.rawValue,
            responseStyle: responseStyle
        )

        print("📡 PetStore: 开始调用PetService.createPet")
        print("📋 PetStore: 请求数据 - name: \(name), breed: \(breed)")

        petService.createPet(request)
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false

                        if case .failure(let error) = completion {
                            print("❌ PetStore: 创建宠物失败 - \(error)")
                            self?.error = self?.handleAPIError(error)
                        } else {
                            print("✅ PetStore: 创建宠物请求完成，开始重新加载宠物列表")
                            // 创建成功后重新加载宠物列表
                            self?.loadUserPets()
                        }
                    }
                },
                receiveValue: { [weak self] newPet in
                    DispatchQueue.main.async {
                        print("✅ PetStore: 创建宠物成功 - \(newPet.name), ID: \(newPet.id)")

                        // 检查宠物是否已存在，避免重复添加
                        if !(self?.pets.contains(where: { $0.id == newPet.id }) ?? false) {
                            self?.pets.append(newPet)
                            print("📝 PetStore: 宠物已添加到列表，当前宠物数量: \(self?.pets.count ?? 0)")
                        }

                        // 如果这是第一只宠物，设为当前宠物
                        if self?.currentPet == nil {
                            self?.currentPet = newPet
                            print("🎯 PetStore: 设置当前宠物为: \(newPet.name)")
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 更新宠物信息
    func updatePet(_ petId: Int, name: String? = nil, breed: String? = nil,
                   age: Int? = nil, gender: PetGender? = nil, color: String? = nil,
                   size: PetSize? = nil, weight: Double? = nil, personality: String? = nil,
                   personalityTags: [String]? = nil, currentMood: PetMood? = nil,
                   responseStyle: String? = nil) {
        isLoading = true
        error = nil
        
        let request = PetUpdateRequest(
            name: name,
            type: nil,
            breed: breed,
            personality: personalityTags,
            avatar: nil,
            birthday: nil,
            weight: weight,
            healthStatus: nil,
            signature: nil,
            mood: currentMood?.rawValue,
            status: nil,
            level: nil,
            experience: nil,
            maxExperience: nil,
            moodDescription: nil,
            aiPersonalityPrompt: nil
        )
        
        petService.updatePet(petId: petId, request: request)
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false
                        
                        if case .failure(let error) = completion {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { [weak self] updatedPet in
                    DispatchQueue.main.async {
                        // 更新宠物列表中的宠物信息
                        if let index = self?.pets.firstIndex(where: { $0.id == petId }) {
                            self?.pets[index] = updatedPet
                        }
                        
                        // 如果是当前宠物，更新当前宠物信息
                        if self?.currentPet?.id == petId {
                            self?.currentPet = updatedPet
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 删除宠物
    func deletePet(_ petId: Int) {
        petService.deletePet(petId: petId)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        DispatchQueue.main.async {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { [weak self] success in
                    if success {
                        DispatchQueue.main.async {
                            self?.pets.removeAll { $0.id == petId }
                            
                            // 如果删除的是当前宠物，选择新的当前宠物
                            if self?.currentPet?.id == petId {
                                self?.currentPet = self?.pets.first
                            }
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 设置当前宠物
    func setCurrentPet(_ pet: Pet) {
        currentPet = pet
    }
    
    // MARK: - 获取宠物详情
    func getPetDetail(_ petId: Int) {
        petService.getPet(petId: petId)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        DispatchQueue.main.async {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { [weak self] pet in
                    DispatchQueue.main.async {
                        // 更新宠物列表中的宠物信息
                        if let index = self?.pets.firstIndex(where: { $0.id == petId }) {
                            self?.pets[index] = pet
                        }
                        
                        // 如果是当前宠物，更新当前宠物信息
                        if self?.currentPet?.id == petId {
                            self?.currentPet = pet
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 获取宠物统计
    func getPetStats() {
        guard let petId = currentPet?.id else { return }
        petService.getPetStats(petId: petId)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        DispatchQueue.main.async {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { stats in
                    // 处理统计数据
                    print("宠物统计: \(stats)")
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 宠物护理操作
    func feedPet(_ petId: Int, notes: String? = nil) {
        petService.feedPet(petId: petId, notes: notes)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        DispatchQueue.main.async {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { success in
                    if success {
                        print("喂食成功")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func walkPet(_ petId: Int, notes: String? = nil) {
        petService.walkPet(petId: petId, notes: notes)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        DispatchQueue.main.async {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { success in
                    if success {
                        print("遛狗成功")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func playWithPet(_ petId: Int, notes: String? = nil) {
        petService.playWithPet(petId: petId, notes: notes)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        DispatchQueue.main.async {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { success in
                    if success {
                        print("玩耍成功")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 错误处理
    private func handleAPIError(_ error: APIError) -> String {
        switch error {
        case .invalidURL:
            return "无效的URL地址"
        case .noData:
            return "服务器没有返回数据"
        case .decodingError:
            return "数据解析错误"
        case .networkError(let underlyingError):
            return "网络连接失败：\(underlyingError.localizedDescription)"
        case .serverError(let code, let message):
            return "服务器错误（\(code)）：\(message ?? "请稍后重试")"
        case .unauthorized:
            return "登录已过期，请重新登录"
        case .timeout:
            return "请求超时，请稍后重试"
        case .noInternetConnection:
            return "无网络连接，请检查网络设置"
        case .rateLimited:
            return "请求过于频繁，请稍后重试"
        case .maintenance:
            return "服务器维护中，请稍后重试"
        case .unknown(let message):
            return "未知错误：\(message)"
        }
    }
    
    // MARK: - 清理缓存
    func clearCache() {
        pets.removeAll()
        currentPet = nil
        error = nil
    }
}

// MARK: - 便利方法
extension PetStore {
    // 根据心情获取宠物
    func getPetsWithMood(_ mood: PetMood) -> [Pet] {
        return pets.filter { $0.currentMood == mood.rawValue }
    }
    
    // 获取活跃宠物
    func getActivePets() -> [Pet] {
        return pets.filter { $0.isActive }
    }
    
    // 根据品种获取宠物
    func getPetsOfBreed(_ breed: String) -> [Pet] {
        return pets.filter {
            return $0.breed.lowercased().contains(breed.lowercased())
        }
    }
}
