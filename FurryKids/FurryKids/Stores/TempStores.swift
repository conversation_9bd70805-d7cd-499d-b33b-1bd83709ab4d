import Foundation
import SwiftUI
import Combine

// MARK: - 互动动作枚举
enum InteractionAction: String, CaseIterable {
    case feed = "喂食"
    case pet = "抚摸"
    case play = "玩耍"
    case walk = "散步"
    case wash = "洗澡"
    case train = "训练"

    var icon: String {
        switch self {
        case .feed: return "🍖"
        case .pet: return "🤗"
        case .play: return "🎾"
        case .walk: return "🚶‍♂️"
        case .wash: return "🛁"
        case .train: return "🎓"
        }
    }
}

// MARK: - 临时Store类 (用于编译)
class FeedStore: ObservableObject {
    @Published var feeds: [Feed] = Feed.sampleData
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    func loadFeeds() {
        // 临时实现
    }
    
    func likeFeed(_ feed: Feed) {
        // 临时实现
    }
}

// PetStore已移至单独文件 Stores/PetStore.swift

class AuthStore: ObservableObject {
    @Published var currentUser: User?
    @Published var isAuthenticated = false
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    func login(username: String, password: String) {
        // 临时实现
    }
    
    func register(username: String, password: String) {
        // 临时实现
    }
    
    func logout() {
        // 临时实现
    }
    
    func clearError() {
        errorMessage = nil
    }
}
