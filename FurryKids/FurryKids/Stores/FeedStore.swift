import Foundation
import Combine

// MARK: - FeedStore (集成后端API)
class FeedStore: ObservableObject {
    @Published var feeds: [Feed] = []
    @Published var isLoading = false
    @Published var isRefreshing = false
    @Published var error: String?
    @Published var hasMoreData = true
    
    private let feedService = FeedService.shared
    private let authService = AuthService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // 分页参数
    private var currentPage = 1
    private let pageSize = 20
    
    init() {
        // 初始加载数据
        loadFeeds()
    }
    
    // MARK: - 加载动态列表
    func loadFeeds(refresh: Bool = false) {
        if refresh {
            currentPage = 1
            hasMoreData = true
            isRefreshing = true
        } else {
            isLoading = true
        }
        
        error = nil
        
        feedService.getFeeds(page: currentPage, limit: pageSize)
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false
                        self?.isRefreshing = false
                        
                        if case .failure(let error) = completion {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { [weak self] newFeeds in
                    DispatchQueue.main.async {
                        if refresh || self?.currentPage == 1 {
                            self?.feeds = newFeeds
                        } else {
                            self?.feeds.append(contentsOf: newFeeds)
                        }
                        
                        // 检查是否还有更多数据
                        self?.hasMoreData = newFeeds.count >= self?.pageSize ?? 20
                        
                        if self?.hasMoreData == true {
                            self?.currentPage += 1
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 加载更多数据
    func loadMoreFeeds() {
        guard hasMoreData && !isLoading else { return }
        loadFeeds()
    }
    
    // MARK: - 刷新数据
    func refreshFeeds() {
        loadFeeds(refresh: true)
    }
    
    // MARK: - 创建动态
    func createFeed(content: String, images: [String] = [], petId: Int, mood: String? = nil, tags: [String] = [], location: String? = nil) {
        print("📡 FeedStore.createFeed 开始")
        print("📝 内容: \(content)")
        print("🐾 宠物ID: \(petId)")
        print("🖼️ 图片: \(images)")

        isLoading = true
        error = nil

        let request = FeedCreateRequest(
            petId: petId,
            content: content,
            images: images,
            tags: tags,
            mood: mood,
            location: location,
            isPublic: true
        )

        print("📦 请求数据: \(request)")
        
        feedService.createFeed(request)
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false

                        if case .failure(let error) = completion {
                            let errorMessage = self?.handleAPIError(error) ?? "未知错误"
                            print("❌ 创建动态失败: \(errorMessage)")
                            self?.error = errorMessage
                        }
                    }
                },
                receiveValue: { [weak self] newFeed in
                    DispatchQueue.main.async {
                        print("✅ 创建动态成功: \(newFeed.id)")
                        // 将新动态插入到列表顶部
                        self?.feeds.insert(newFeed, at: 0)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 点赞动态
    func likeFeed(_ feedId: Int) {
        feedService.likeFeed(feedId: feedId)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        DispatchQueue.main.async {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { [weak self] response in
                    DispatchQueue.main.async {
                        // 更新本地数据
                        if let index = self?.feeds.firstIndex(where: { $0.id == feedId }) {
                            var updatedFeed = self?.feeds[index]
                            updatedFeed?.isLiked = response.isLiked
                            updatedFeed?.likesCount = response.likesCount
                            if let feed = updatedFeed {
                                self?.feeds[index] = feed
                            }
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 获取用户动态
    func loadUserFeeds(userId: Int) {
        isLoading = true
        error = nil
        
        feedService.getUserFeeds(userId: userId, page: 1, limit: pageSize)
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false
                        
                        if case .failure(let error) = completion {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { [weak self] userFeeds in
                    DispatchQueue.main.async {
                        self?.feeds = userFeeds
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 获取宠物动态
    func loadPetFeeds(petId: Int) {
        isLoading = true
        error = nil
        
        feedService.getPetFeeds(petId: petId, page: 1, limit: pageSize)
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false
                        
                        if case .failure(let error) = completion {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { [weak self] petFeeds in
                    DispatchQueue.main.async {
                        self?.feeds = petFeeds
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 删除动态
    func deleteFeed(_ feedId: Int) {
        feedService.deleteFeed(feedId: feedId)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        DispatchQueue.main.async {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { [weak self] success in
                    if success {
                        DispatchQueue.main.async {
                            self?.feeds.removeAll { $0.id == feedId }
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 添加评论
    func addComment(to feedId: Int, content: String, parentId: Int? = nil) {
        feedService.addComment(feedId: feedId, content: content, parentId: parentId)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        DispatchQueue.main.async {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { [weak self] comment in
                    DispatchQueue.main.async {
                        // 更新评论数
                        if let index = self?.feeds.firstIndex(where: { $0.id == feedId }) {
                            var updatedFeed = self?.feeds[index]
                            updatedFeed?.commentsCount += 1
                            if let feed = updatedFeed {
                                self?.feeds[index] = feed
                            }
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 错误处理
    private func handleAPIError(_ error: APIError) -> String {
        switch error {
        case .invalidURL:
            return "无效的URL地址"
        case .noData:
            return "服务器没有返回数据"
        case .decodingError:
            return "数据解析错误"
        case .networkError(let underlyingError):
            return "网络连接失败：\(underlyingError.localizedDescription)"
        case .serverError(let code, let message):
            return "服务器错误（\(code)）：\(message ?? "请稍后重试")"
        case .unauthorized:
            return "登录已过期，请重新登录"
        case .timeout:
            return "请求超时，请稍后重试"
        case .noInternetConnection:
            return "无网络连接，请检查网络设置"
        case .rateLimited:
            return "请求过于频繁，请稍后重试"
        case .maintenance:
            return "服务器维护中，请稍后重试"
        case .unknown(let message):
            return "未知错误：\(message)"
        }
    }
    
    // MARK: - 清理缓存
    func clearCache() {
        feeds.removeAll()
        currentPage = 1
        hasMoreData = true
        error = nil
    }
}

// MARK: - 便利方法
extension FeedStore {
    // 根据宠物ID获取动态
    func getFeedsForPet(_ petId: Int) -> [Feed] {
        return feeds.filter { $0.petId == petId }
    }
    
    // 获取特定心情的动态
    func getFeedsWithMood(_ mood: String) -> [Feed] {
        return feeds.filter { $0.mood == mood }
    }
    
    // 获取包含特定标签的动态
    func getFeedsWithTag(_ tag: String) -> [Feed] {
        return feeds.filter { $0.tags.contains(tag) }
    }
}
