import Foundation
import Combine
import SwiftUI

class AuthStore: ObservableObject {
    @Published var currentUser: User?
    @Published var isAuthenticated = false
    @Published var isLoading = false
    @Published var errorMessage: String?

    // 公开cancellables属性
    var cancellables = Set<AnyCancellable>()
    private let authService = AuthService.shared

    var isLoggedIn: Bool {
        return isAuthenticated && currentUser != nil
    }

    init() {
        checkAuthStatus()
    }
    
    // MARK: - 检查认证状态
    func checkAuthStatus() {
        print("🔍 AuthStore: 开始检查认证状态...")
        isLoading = true

        // 检查是否有存储的token
        if let token = APIConfig.authToken {
            print("🎫 AuthStore: 找到存储的token: \(token.prefix(20))...")
        } else {
            print("❌ AuthStore: 没有找到存储的token")
            DispatchQueue.main.async {
                self.isLoading = false
                self.isAuthenticated = false
                self.currentUser = nil
            }
            return
        }

        authService.getCurrentUser()
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false
                        if case .failure(let error) = completion {
                            print("❌ AuthStore: 认证状态检查失败: \(error)")
                            // 清除无效的token
                            APIConfig.authToken = nil
                            self?.isAuthenticated = false
                            self?.currentUser = nil
                        }
                    }
                },
                receiveValue: { [weak self] response in
                    DispatchQueue.main.async {
                        if let user = response.user {
                            print("✅ AuthStore: 认证状态有效，用户: \(user.username)")
                            self?.currentUser = user
                            self?.isAuthenticated = true
                        } else {
                            print("❌ AuthStore: 认证响应中没有用户信息")
                            // 清除无效的token
                            APIConfig.authToken = nil
                            self?.isAuthenticated = false
                            self?.currentUser = nil
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 用户登录
    func login(username: String, password: String) async {
        print("🔐 AuthStore: 开始登录，用户名: \(username)")
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }

        authService.login(username: username, password: password)
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false
                        if case .failure(let error) = completion {
                            print("❌ AuthStore: 登录请求失败: \(error)")
                            self?.errorMessage = "网络错误: \(error.localizedDescription)"
                        }
                    }
                },
                receiveValue: { [weak self] response in
                    DispatchQueue.main.async {
                        print("📨 AuthStore: 收到登录响应: success=\(response.success)")
                        if response.success, let user = response.user {
                            print("✅ AuthStore: 登录成功，用户: \(user.username)")
                            self?.currentUser = user
                            self?.isAuthenticated = true
                            self?.errorMessage = nil
                        } else {
                            print("❌ AuthStore: 登录失败: \(response.message)")
                            self?.errorMessage = response.message
                            self?.isAuthenticated = false
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 用户注册
    func register(username: String, password: String, email: String? = nil) async {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }

        authService.register(username: username, password: password, email: email)
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false
                        if case .failure(let error) = completion {
                            self?.errorMessage = "网络错误: \(error.localizedDescription)"
                        }
                    }
                },
                receiveValue: { [weak self] response in
                    DispatchQueue.main.async {
                        if response.success, let user = response.user {
                            self?.currentUser = user
                            self?.isAuthenticated = true
                            self?.errorMessage = nil
                        } else {
                            self?.errorMessage = response.message
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 用户登出
    func logout() {
        isLoading = true
        
        authService.logout()
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false
                        if case .failure(let error) = completion {
                            self?.errorMessage = "登出失败: \(error.localizedDescription)"
                        }
                    }
                },
                receiveValue: { [weak self] response in
                    DispatchQueue.main.async {
                        if response.success {
                            self?.currentUser = nil
                            self?.isAuthenticated = false
                            self?.errorMessage = nil
                        } else {
                            self?.errorMessage = response.message
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 清除错误信息
    func clearError() {
        errorMessage = nil
    }
} 