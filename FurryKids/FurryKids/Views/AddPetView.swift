import SwiftUI

struct AddPetView: View {
    @EnvironmentObject var petStore: PetStore
    @Environment(\.dismiss) private var dismiss
    
    @State private var name = ""
    @State private var breed = ""
    @State private var selectedType = "dog"
    @State private var selectedAvatar = "🐕"
    @State private var weight = ""
    @State private var selectedPersonality: [String] = []
    @State private var signature = ""
    @State private var selectedMood = "开心"
    
    @State private var showingError = false
    @State private var errorMessage = ""
    
    // 宠物类型选项
    private let petTypes = [
        ("dog", "狗狗", "🐕"),
        ("cat", "猫咪", "🐱"),
        ("rabbit", "兔子", "🐰"),
        ("bird", "鸟类", "🐦"),
        ("fish", "鱼类", "🐠"),
        ("hamster", "仓鼠", "🐹")
    ]
    
    // 头像选项
    private let avatarOptions = [
        "🐕", "🐶", "🦮", "🐕‍🦺", "🐩",
        "🐱", "🐈", "🐈‍⬛", "🦁", "🐯",
        "🐰", "🐹", "🐭", "🐦", "🐧",
        "🐠", "🐟", "🐡", "🦈", "🐙"
    ]
    
    // 性格标签
    private let personalityOptions = [
        "活泼", "安静", "友善", "独立", "粘人",
        "聪明", "调皮", "温顺", "勇敢", "害羞",
        "好奇", "懒惰", "忠诚", "机警", "温柔"
    ]
    
    // 心情选项
    private let moodOptions = ["开心", "平静", "兴奋", "困倦", "好奇"]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 头像选择
                    avatarSection
                    
                    // 基本信息
                    basicInfoSection
                    
                    // 性格标签
                    personalitySection
                    
                    // 其他信息
                    additionalInfoSection
                }
                .padding(.horizontal, 16)
                .padding(.top, 16)
                .padding(.bottom, 100)
            }
            .background(Color(hex: "F9FAFB"))
            .navigationTitle("添加宠物")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        savePet()
                    }
                    .disabled(name.isEmpty || breed.isEmpty)
                }
            }
        }
        .alert("错误", isPresented: $showingError) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(errorMessage)
        }
    }
    
    // MARK: - 头像选择区域
    private var avatarSection: some View {
        VStack(spacing: 16) {
            Text("选择头像")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(Color(hex: "101618"))
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 当前选中的头像
            Text(selectedAvatar)
                .font(.system(size: 60))
                .frame(width: 100, height: 100)
                .background(
                    LinearGradient(
                        colors: [Color(hex: "eaeff1"), Color(hex: "f5f7f8")],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(Color(hex: "5c7d8a"), lineWidth: 2)
                )
            
            // 头像选择网格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 5), spacing: 8) {
                ForEach(avatarOptions, id: \.self) { avatar in
                    Button(action: {
                        selectedAvatar = avatar
                    }) {
                        Text(avatar)
                            .font(.system(size: 24))
                            .frame(width: 44, height: 44)
                            .background(
                                selectedAvatar == avatar ? 
                                Color(hex: "5c7d8a").opacity(0.2) : 
                                Color(hex: "eaeff1").opacity(0.5)
                            )
                            .clipShape(Circle())
                            .overlay(
                                Circle()
                                    .stroke(
                                        selectedAvatar == avatar ? 
                                        Color(hex: "5c7d8a") : Color.clear,
                                        lineWidth: 2
                                    )
                            )
                    }
                }
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    // MARK: - 基本信息区域
    private var basicInfoSection: some View {
        VStack(spacing: 16) {
            Text("基本信息")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(Color(hex: "101618"))
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 16) {
                // 宠物名称
                VStack(alignment: .leading, spacing: 8) {
                    Text("宠物名称 *")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    ChineseTextField(
                        text: $name,
                        placeholder: "请输入宠物名称",
                        keyboardType: .default
                    )
                    .frame(height: 48)
                }
                
                // 宠物类型
                VStack(alignment: .leading, spacing: 8) {
                    Text("宠物类型")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(petTypes, id: \.0) { type in
                                Button(action: {
                                    selectedType = type.0
                                    // 根据类型自动选择合适的头像
                                    selectedAvatar = type.2
                                }) {
                                    HStack(spacing: 6) {
                                        Text(type.2)
                                            .font(.system(size: 16))
                                        Text(type.1)
                                            .font(.system(size: 14, weight: .medium))
                                    }
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 8)
                                    .background(
                                        selectedType == type.0 ? 
                                        Color(hex: "5c7d8a") : 
                                        Color(hex: "eaeff1")
                                    )
                                    .foregroundColor(
                                        selectedType == type.0 ? 
                                        .white : 
                                        Color(hex: "101618")
                                    )
                                    .cornerRadius(20)
                                }
                            }
                        }
                        .padding(.horizontal, 2)
                    }
                }
                
                // 品种
                VStack(alignment: .leading, spacing: 8) {
                    Text("品种 *")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    ChineseTextField(
                        text: $breed,
                        placeholder: "请输入品种",
                        keyboardType: .default
                    )
                    .frame(height: 48)
                }
                
                // 体重
                VStack(alignment: .leading, spacing: 8) {
                    Text("体重 (kg)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    ChineseTextField(
                        text: $weight,
                        placeholder: "请输入体重",
                        keyboardType: .decimalPad
                    )
                    .frame(height: 48)
                }
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    // MARK: - 性格标签区域
    private var personalitySection: some View {
        VStack(spacing: 16) {
            Text("性格标签")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(Color(hex: "101618"))
                .frame(maxWidth: .infinity, alignment: .leading)
            
            Text("选择最多5个标签来描述您的宠物性格")
                .font(.system(size: 12))
                .foregroundColor(Color(hex: "5c7d8a"))
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 3), spacing: 8) {
                ForEach(personalityOptions, id: \.self) { personality in
                    Button(action: {
                        togglePersonality(personality)
                    }) {
                        Text(personality)
                            .font(.system(size: 14, weight: .medium))
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                selectedPersonality.contains(personality) ? 
                                Color(hex: "5c7d8a") : 
                                Color(hex: "eaeff1")
                            )
                            .foregroundColor(
                                selectedPersonality.contains(personality) ? 
                                .white : 
                                Color(hex: "101618")
                            )
                            .cornerRadius(16)
                    }
                    .disabled(selectedPersonality.count >= 5 && !selectedPersonality.contains(personality))
                }
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    // MARK: - 其他信息区域
    private var additionalInfoSection: some View {
        VStack(spacing: 16) {
            Text("其他信息")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(Color(hex: "101618"))
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 16) {
                // 个性签名
                VStack(alignment: .leading, spacing: 8) {
                    Text("个性签名")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    ChineseTextEditor(
                        text: $signature,
                        placeholder: "为您的宠物写一句话...",
                        font: UIFont.systemFont(ofSize: 16),
                        textColor: UIColor(red: 0.063, green: 0.086, blue: 0.094, alpha: 1.0),
                        backgroundColor: UIColor(red: 0.976, green: 0.980, blue: 0.984, alpha: 1.0)
                    )
                    .frame(height: 80)
                }
                
                // 当前心情
                VStack(alignment: .leading, spacing: 8) {
                    Text("当前心情")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(moodOptions, id: \.self) { mood in
                                Button(action: {
                                    selectedMood = mood
                                }) {
                                    Text(mood)
                                        .font(.system(size: 14, weight: .medium))
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(
                                            selectedMood == mood ? 
                                            Color(hex: "5c7d8a") : 
                                            Color(hex: "eaeff1")
                                        )
                                        .foregroundColor(
                                            selectedMood == mood ? 
                                            .white : 
                                            Color(hex: "101618")
                                        )
                                        .cornerRadius(20)
                                }
                            }
                        }
                        .padding(.horizontal, 2)
                    }
                }
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    // MARK: - 辅助方法
    private func togglePersonality(_ personality: String) {
        if selectedPersonality.contains(personality) {
            selectedPersonality.removeAll { $0 == personality }
        } else if selectedPersonality.count < 5 {
            selectedPersonality.append(personality)
        }
    }
    
    private func savePet() {
        guard !name.isEmpty, !breed.isEmpty else {
            errorMessage = "请填写必填项"
            showingError = true
            return
        }

        // 添加调试信息
        print("🐾 开始添加宠物: \(name), 品种: \(breed)")
        print("🎭 AddPetView: selectedAvatar=\(selectedAvatar)")
        if let token = APIConfig.authToken {
            print("🔐 当前认证token: \(token.prefix(20))...")
        } else {
            print("❌ 没有找到认证token")
            errorMessage = "认证已过期，请重新登录"
            showingError = true
            return
        }

        let weightValue = Double(weight)

        petStore.createPet(
            name: name,
            breed: breed,
            weight: weightValue,
            personalityTags: selectedPersonality,
            avatarUrl: selectedAvatar  // 传递选择的头像
        )

        dismiss()
    }
}

// MARK: - 自定义文本框样式
struct CustomTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 12)
            .padding(.vertical, 12)
            .background(Color(hex: "F9FAFB"))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(hex: "eaeff1"), lineWidth: 1)
            )
    }
}

#Preview {
    AddPetView()
        .environmentObject(PetStore())
}
