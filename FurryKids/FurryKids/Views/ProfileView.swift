import SwiftUI

struct ProfileView: View {
    @EnvironmentObject var petStore: PetStore
    @EnvironmentObject var authService: AuthService
    @State private var showingAddPet = false
    @State private var showingPetDetail = false
    @State private var selectedPet: Pet?
    @State private var showingDeleteAlert = false
    @State private var petToDelete: Pet?

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 0) {
                    // 宠物管理区域
                    petManagementSection

                    // 设置菜单
                    settingsSection

                    Spacer(minLength: 100)
                }
            }
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(hex: "F8FAFC"),
                        Color(hex: "F1F5F9")
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .navigationTitle("宠物档案")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                petStore.loadUserPets()
            }
        }
        .sheet(isPresented: $showingAddPet) {
            SimpleAddPetView()
                .environmentObject(petStore)
        }
        .sheet(isPresented: $showingPetDetail) {
            if let pet = selectedPet {
                SimplePetDetailView(pet: pet)
                    .environmentObject(petStore)
            }
        }
        .alert("删除宠物", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                if let pet = petToDelete {
                    petStore.deletePet(pet.id)
                }
            }
        } message: {
            Text("确定要删除宠物「\(petToDelete?.name ?? "")」吗？此操作无法撤销。")
        }
        .onAppear {
            petStore.loadUserPets()
        }
    }




    // MARK: - 宠物管理区域
    private var petManagementSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 标题和添加按钮
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("我的宠物")
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(Color(hex: "1E293B"))

                    Text("管理您的毛孩子们")
                        .font(.system(size: 14))
                        .foregroundColor(Color(hex: "64748B"))
                }

                Spacer()

                Button(action: {
                    showingAddPet = true
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 16, weight: .medium))
                        Text("添加宠物")
                            .font(.system(size: 15, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(hex: "3B82F6"),
                                Color(hex: "2563EB")
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(25)
                    .shadow(color: Color(hex: "3B82F6").opacity(0.3), radius: 8, x: 0, y: 4)
                }
            }
            .padding(.horizontal, 20)

            // 宠物列表
            if petStore.pets.isEmpty {
                emptyPetState
            } else {
                petListView
            }
        }
        .padding(.top, 20)
    }

    // 空状态视图
    private var emptyPetState: some View {
        VStack(spacing: 24) {
            // 插画区域
            VStack(spacing: 16) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(hex: "F1F5F9"),
                                    Color(hex: "E2E8F0")
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 120, height: 120)

                    Image(systemName: "pawprint.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(Color(hex: "64748B"))
                }

                VStack(spacing: 12) {
                    Text("还没有宠物档案")
                        .font(.system(size: 22, weight: .bold))
                        .foregroundColor(Color(hex: "1E293B"))

                    Text("添加您的第一个毛孩子\n开始记录美好时光")
                        .font(.system(size: 16))
                        .foregroundColor(Color(hex: "64748B"))
                        .multilineTextAlignment(.center)
                        .lineSpacing(4)
                }
            }

            Button(action: {
                showingAddPet = true
            }) {
                HStack(spacing: 10) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 18))
                    Text("添加第一个宠物")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 28)
                .padding(.vertical, 16)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "3B82F6"),
                            Color(hex: "2563EB")
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(30)
                .shadow(color: Color(hex: "3B82F6").opacity(0.3), radius: 12, x: 0, y: 6)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
        .padding(.horizontal, 24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.08), radius: 20, x: 0, y: 8)
        )
        .padding(.horizontal, 20)
    }

    // 宠物列表视图
    private var petListView: some View {
        LazyVStack(spacing: 16) {
            ForEach(petStore.pets) { pet in
                PetRowCardView(
                    pet: pet,
                    isSelected: pet.id == petStore.currentPet?.id,
                    onTap: {
                        petStore.setCurrentPet(pet)
                        selectedPet = pet
                        showingPetDetail = true
                    },
                    onEdit: {
                        selectedPet = pet
                        showingPetDetail = true
                    },
                    onDelete: {
                        petToDelete = pet
                        showingDeleteAlert = true
                    }
                )
            }
        }
        .padding(.horizontal, 20)
    }

    // MARK: - 设置区域
    private var settingsSection: some View {
        VStack(spacing: 1) {
            ProfileMenuItemView(icon: "gear", title: "设置", isFirst: true) {
                // TODO: 打开设置页面
            }
            ProfileMenuItemView(icon: "questionmark.circle", title: "帮助与反馈") {
                // TODO: 打开帮助页面
            }
            ProfileMenuItemView(icon: "info.circle", title: "关于我们", isLast: true) {
                // TODO: 打开关于页面
            }
        }
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.08), radius: 20, x: 0, y: 8)
        .padding(.horizontal, 20)
        .padding(.top, 32)
    }
}

// MARK: - 宠物行卡片视图（左右布局）
struct PetRowCardView: View {
    let pet: Pet
    let isSelected: Bool
    let onTap: () -> Void
    let onEdit: () -> Void
    let onDelete: () -> Void

    @State private var showingActionSheet = false

    var body: some View {
        HStack(spacing: 16) {
            // 左侧：头像和状态
            ZStack(alignment: .bottomTrailing) {
                // 头像
                Text(pet.avatar ?? "🐕")
                    .font(.system(size: 32))
                    .frame(width: 64, height: 64)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(hex: "F8FAFC"),
                                Color(hex: "F1F5F9")
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .clipShape(Circle())
                    .overlay(
                        Circle()
                            .stroke(
                                isSelected ?
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "3B82F6"),
                                        Color(hex: "2563EB")
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ) :
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "E2E8F0"),
                                        Color(hex: "E2E8F0")
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: isSelected ? 3 : 2
                            )
                    )

                // 在线状态指示器
                if pet.isActive {
                    Circle()
                        .fill(Color(hex: "10B981"))
                        .frame(width: 12, height: 12)
                        .overlay(
                            Circle()
                                .stroke(Color.white, lineWidth: 2)
                        )
                        .offset(x: 2, y: 2)
                }
            }

            // 中间：宠物信息
            VStack(alignment: .leading, spacing: 8) {
                // 名称和品种
                VStack(alignment: .leading, spacing: 4) {
                    Text(pet.name)
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(Color(hex: "1E293B"))
                        .lineLimit(1)

                    Text(pet.breed)
                        .font(.system(size: 14))
                        .foregroundColor(Color(hex: "64748B"))
                        .lineLimit(1)
                }

                // 等级和心情标签
                HStack(spacing: 8) {
                    // 等级标签
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .font(.system(size: 10))
                            .foregroundColor(Color(hex: "F59E0B"))
                        Text("Lv.\(pet.level)")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(Color(hex: "92400E"))
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(hex: "FEF3C7"))
                    .cornerRadius(12)

                    // 心情标签
                    Text(pet.mood)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(moodColor(pet.mood))
                        .cornerRadius(12)

                    Spacer()
                }
            }

            Spacer()

            // 右侧：操作按钮
            Button(action: {
                showingActionSheet = true
            }) {
                Image(systemName: "ellipsis")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color(hex: "64748B"))
                    .frame(width: 32, height: 32)
                    .background(Color(hex: "F8FAFC"))
                    .clipShape(Circle())
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.06), radius: 12, x: 0, y: 4)
        .onTapGesture {
            onTap()
        }
        .confirmationDialog("宠物操作", isPresented: $showingActionSheet) {
            Button("查看详情") {
                onTap()
            }
            Button("编辑信息") {
                onEdit()
            }
            Button("删除宠物", role: .destructive) {
                onDelete()
            }
            Button("取消", role: .cancel) { }
        }
    }

    // 根据心情返回对应颜色
    private func moodColor(_ mood: String) -> Color {
        switch mood {
        case "开心", "快乐":
            return Color(hex: "10B981")
        case "平静", "安静":
            return Color(hex: "3B82F6")
        case "兴奋", "活跃":
            return Color(hex: "F59E0B")
        case "疲惫", "困倦":
            return Color(hex: "6B7280")
        default:
            return Color(hex: "64748B")
        }
    }
}

// MARK: - 宠物卡片视图
struct PetCardView: View {
    let pet: Pet
    let isSelected: Bool
    let onTap: () -> Void
    let onEdit: () -> Void
    let onDelete: () -> Void

    @State private var showingActionSheet = false

    var body: some View {
        VStack(spacing: 4) {
            // 头像和操作按钮区域
            HStack {
                // 头像和在线状态
                ZStack(alignment: .bottomTrailing) {
                    // 头像
                    Text(pet.avatar ?? "🐕")
                        .font(.system(size: 24))
                        .frame(width: 44, height: 44)
                        .background(Color(hex: "f8f9fa"))
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(
                                    isSelected ? Color(hex: "007AFF") : Color(hex: "e9ecef"),
                                    lineWidth: isSelected ? 2 : 1
                                )
                        )

                    // 在线状态指示器
                    if pet.isActive {
                        Circle()
                            .fill(Color(hex: "34C759"))
                            .frame(width: 8, height: 8)
                            .overlay(
                                Circle()
                                    .stroke(Color.white, lineWidth: 1)
                            )
                    }
                }

                Spacer()

                // 更多操作按钮
                Button(action: {
                    showingActionSheet = true
                }) {
                    Image(systemName: "ellipsis")
                        .font(.system(size: 8, weight: .medium))
                        .foregroundColor(Color(hex: "8e9aaf"))
                        .frame(width: 16, height: 16)
                        .background(Color(hex: "f8f9fa"))
                        .clipShape(Circle())
                }
            }

            // 宠物信息
            VStack(alignment: .leading, spacing: 2) {
                // 名称
                Text(pet.name)
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(Color(hex: "1d1d1f"))
                    .lineLimit(1)

                // 品种
                Text(pet.breed)
                    .font(.system(size: 9))
                    .foregroundColor(Color(hex: "8e9aaf"))
                    .lineLimit(1)
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // 等级和心情
            HStack(spacing: 6) {
                // 等级
                HStack(spacing: 2) {
                    Image(systemName: "star.fill")
                        .font(.system(size: 6))
                        .foregroundColor(Color(hex: "FF9500"))
                    Text("Lv.\(pet.level)")
                        .font(.system(size: 7, weight: .medium))
                        .foregroundColor(Color(hex: "8e9aaf"))
                }
                .padding(.horizontal, 4)
                .padding(.vertical, 2)
                .background(Color(hex: "f8f9fa"))
                .cornerRadius(4)

                Spacer()

                // 心情
                Text(pet.mood)
                    .font(.system(size: 7, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 4)
                    .padding(.vertical, 2)
                    .background(moodColor(pet.mood))
                    .cornerRadius(4)
            }
        }
        .padding(8)
        .background(Color.white)
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color(hex: "f1f3f4"), lineWidth: 1)
        )
        .onTapGesture {
            onTap()
        }
        .confirmationDialog("宠物操作", isPresented: $showingActionSheet) {
            Button("查看详情") {
                onTap()
            }
            Button("编辑信息") {
                onEdit()
            }
            Button("删除宠物", role: .destructive) {
                onDelete()
            }
            Button("取消", role: .cancel) { }
        }
    }

    // 根据心情返回对应颜色
    private func moodColor(_ mood: String) -> Color {
        switch mood {
        case "开心", "快乐":
            return .green
        case "平静", "安静":
            return .blue
        case "兴奋", "活跃":
            return .orange
        case "疲惫", "困倦":
            return .gray
        default:
            return Color(hex: "5c7d8a")
        }
    }
}

// MARK: - 菜单项视图
struct ProfileMenuItemView: View {
    let icon: String
    let title: String
    let isFirst: Bool
    let isLast: Bool
    let action: () -> Void

    init(icon: String, title: String, isFirst: Bool = false, isLast: Bool = false, action: @escaping () -> Void) {
        self.icon = icon
        self.title = title
        self.isFirst = isFirst
        self.isLast = isLast
        self.action = action
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // 图标背景
                ZStack {
                    Circle()
                        .fill(Color(hex: "F1F5F9"))
                        .frame(width: 36, height: 36)

                    Image(systemName: icon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(Color(hex: "3B82F6"))
                }

                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color(hex: "1E293B"))

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color(hex: "94A3B8"))
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .background(Color.white)
        .overlay(
            Group {
                if !isLast {
                    Rectangle()
                        .frame(height: 1)
                        .foregroundColor(Color(hex: "F1F5F9"))
                        .padding(.leading, 72)
                }
            },
            alignment: .bottom
        )
    }
}

// MARK: - 简化的添加宠物视图
struct SimpleAddPetView: View {
    @EnvironmentObject var petStore: PetStore
    @Environment(\.dismiss) private var dismiss

    @State private var name = ""
    @State private var breed = ""
    @State private var selectedAvatar = "🐕"
    @State private var isLoading = false
    @State private var showingError = false
    @State private var errorMessage = ""

    private let avatarOptions = ["🐕", "🐶", "🐱", "🐈", "🐰", "🐹", "🐦", "🐠"]

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 头像选择
                VStack(spacing: 16) {
                    Text("选择头像")
                        .font(.headline)

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 16) {
                        ForEach(avatarOptions, id: \.self) { avatar in
                            Button(action: {
                                selectedAvatar = avatar
                            }) {
                                Text(avatar)
                                    .font(.system(size: 32))
                                    .frame(width: 60, height: 60)
                                    .background(selectedAvatar == avatar ? Color.blue.opacity(0.2) : Color.gray.opacity(0.1))
                                    .cornerRadius(12)
                            }
                        }
                    }
                }

                // 基本信息
                VStack(spacing: 16) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("宠物名称")
                            .font(.headline)
                        TextField("请输入宠物名称", text: $name)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }

                    VStack(alignment: .leading, spacing: 8) {
                        Text("品种")
                            .font(.headline)
                        TextField("请输入品种（可选）", text: $breed)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }
                }

                Spacer()

                // 保存按钮
                Button(action: savePet) {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else {
                        Text("保存")
                            .font(.headline)
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(name.isEmpty ? Color.gray : Color.blue)
                .cornerRadius(12)
                .disabled(name.isEmpty || isLoading)
            }
            .padding()
            .navigationTitle("添加宠物")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
        .alert("错误", isPresented: $showingError) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(errorMessage)
        }
    }

    private func savePet() {
        guard !name.isEmpty else { return }

        // 检查认证状态
        guard APIConfig.authToken != nil else {
            errorMessage = "请先登录后再添加宠物"
            showingError = true
            return
        }

        isLoading = true

        // 调用PetStore的createPet方法
        petStore.createPet(
            name: name,
            breed: breed.isEmpty ? "未知品种" : breed,
            personalityTags: []
        )

        // 添加一个短暂的延迟后关闭页面，让用户看到操作完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            isLoading = false
            dismiss()
        }
    }
}

// MARK: - 简化的宠物详情视图
struct SimplePetDetailView: View {
    @EnvironmentObject var petStore: PetStore
    @Environment(\.dismiss) private var dismiss

    let pet: Pet
    @State private var showingDeleteAlert = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 宠物头像和基本信息
                    VStack(spacing: 16) {
                        Text(pet.avatar ?? "🐕")
                            .font(.system(size: 80))

                        VStack(spacing: 8) {
                            Text(pet.name)
                                .font(.title)
                                .fontWeight(.bold)

                            Text(pet.breed)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }

                    // 详细信息
                    VStack(spacing: 16) {
                        InfoRow(title: "心情", value: pet.mood)
                        InfoRow(title: "等级", value: "Lv.\(pet.level)")
                        InfoRow(title: "个性签名", value: pet.signature)

                        if !pet.personalityTags.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("性格标签")
                                    .font(.headline)

                                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                                    ForEach(pet.personalityTags, id: \.self) { trait in
                                        Text(trait)
                                            .font(.caption)
                                            .padding(.horizontal, 12)
                                            .padding(.vertical, 6)
                                            .background(Color.blue.opacity(0.1))
                                            .foregroundColor(.blue)
                                            .cornerRadius(12)
                                    }
                                }
                            }
                        }
                    }

                    Spacer(minLength: 50)

                    // 删除按钮
                    Button(action: {
                        showingDeleteAlert = true
                    }) {
                        Text("删除宠物")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(Color.red)
                            .cornerRadius(12)
                    }
                }
                .padding()
            }
            .navigationTitle("宠物详情")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
            .alert("确认删除", isPresented: $showingDeleteAlert) {
                Button("取消", role: .cancel) { }
                Button("删除", role: .destructive) {
                    petStore.deletePet(pet.id)
                    dismiss()
                }
            } message: {
                Text("确定要删除\(pet.name)吗？此操作无法撤销。")
            }
        }
    }
}

struct InfoRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .font(.headline)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .font(.body)
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
}

#Preview {
    ProfileView()
        .environmentObject(PetStore())
        .environmentObject(AuthService.shared)
}