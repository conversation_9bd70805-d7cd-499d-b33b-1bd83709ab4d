import SwiftUI

struct RegisterView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var authStore: AuthStore
    
    @State private var username = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var email = ""
    @State private var isLoading = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部区域
                VStack(spacing: 20) {
                    // Logo
                    Image(systemName: "pawprint.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(Color(hex: "101618"))
                    
                    VStack(spacing: 8) {
                        Text("创建账号")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(Color(hex: "101618"))
                        
                        Text("加入毛孩子AI大家庭")
                            .font(.body)
                            .foregroundColor(Color(hex: "5c7d8a"))
                    }
                }
                .padding(.top, 40)
                .padding(.bottom, 40)
                
                // 表单区域
                ScrollView {
                    VStack(spacing: 20) {
                        // 用户名输入框
                        VStack(alignment: .leading, spacing: 8) {
                            Text("用户名")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(Color(hex: "101618"))
                            
                            ChineseTextField(
                                text: $username,
                                placeholder: "请输入用户名",
                                keyboardType: .default
                            )
                            .frame(height: 48)
                        }
                        
                        // 邮箱输入框
                        VStack(alignment: .leading, spacing: 8) {
                            Text("邮箱")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(Color(hex: "101618"))
                            
                            ChineseTextField(
                                text: $email,
                                placeholder: "请输入邮箱地址",
                                keyboardType: .emailAddress
                            )
                            .frame(height: 48)
                        }
                        
                        // 密码输入框
                        VStack(alignment: .leading, spacing: 8) {
                            Text("密码")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(Color(hex: "101618"))
                            
                            ChineseTextField(
                                text: $password,
                                placeholder: "请输入密码",
                                isSecure: true,
                                keyboardType: .default
                            )
                            .frame(height: 48)
                        }
                        
                        // 确认密码输入框
                        VStack(alignment: .leading, spacing: 8) {
                            Text("确认密码")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(Color(hex: "101618"))
                            
                            ChineseTextField(
                                text: $confirmPassword,
                                placeholder: "请再次输入密码",
                                isSecure: true,
                                keyboardType: .default
                            )
                            .frame(height: 48)
                        }
                        
                        // 密码强度提示
                        if !password.isEmpty {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("密码强度:")
                                    .font(.system(size: 12))
                                    .foregroundColor(Color(hex: "5c7d8a"))
                                
                                HStack(spacing: 4) {
                                    ForEach(0..<4) { index in
                                        Rectangle()
                                            .frame(height: 4)
                                            .foregroundColor(passwordStrengthColor(index: index))
                                            .cornerRadius(2)
                                    }
                                    
                                    Spacer()
                                    
                                    Text(passwordStrengthText)
                                        .font(.system(size: 12))
                                        .foregroundColor(passwordStrengthColor(index: 0))
                                }
                            }
                        }
                        
                        // 注册按钮
                        Button(action: {
                            register()
                        }) {
                            HStack {
                                if isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                }
                                
                                Text(isLoading ? "注册中..." : "注册")
                                    .font(.system(size: 16, weight: .semibold))
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(
                                Color(hex: "101618")
                                    .opacity(canRegister ? 1.0 : 0.6)
                            )
                            .cornerRadius(25)
                        }
                        .disabled(!canRegister || isLoading)
                        .padding(.top, 10)
                        
                        // 用户协议
                        VStack(spacing: 8) {
                            Text("注册即表示您同意我们的")
                                .font(.system(size: 12))
                                .foregroundColor(Color(hex: "5c7d8a"))
                            
                            HStack(spacing: 4) {
                                Button("用户协议") {
                                    // TODO: 显示用户协议
                                }
                                .font(.system(size: 12))
                                .foregroundColor(Color(hex: "101618"))
                                
                                Text("和")
                                    .font(.system(size: 12))
                                    .foregroundColor(Color(hex: "5c7d8a"))
                                
                                Button("隐私政策") {
                                    // TODO: 显示隐私政策
                                }
                                .font(.system(size: 12))
                                .foregroundColor(Color(hex: "101618"))
                            }
                        }
                        .padding(.top, 10)
                    }
                    .padding(.horizontal, 30)
                }
                
                // 底部登录提示
                HStack {
                    Text("已有账号？")
                        .font(.system(size: 14))
                        .foregroundColor(Color(hex: "5c7d8a"))
                    
                    Button(action: {
                        dismiss()
                    }) {
                        Text("立即登录")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color(hex: "101618"))
                    }
                }
                .padding(.bottom, 30)
            }
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                    .foregroundColor(Color(hex: "5c7d8a"))
                }
            }
        }
        .alert("注册失败", isPresented: $showingAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private var canRegister: Bool {
        !username.isEmpty && 
        !email.isEmpty && 
        !password.isEmpty && 
        !confirmPassword.isEmpty &&
        password == confirmPassword &&
        password.count >= 6 &&
        isValidEmail(email)
    }
    
    private var passwordStrength: Int {
        var strength = 0
        if password.count >= 6 { strength += 1 }
        if password.count >= 8 { strength += 1 }
        if password.rangeOfCharacter(from: .decimalDigits) != nil { strength += 1 }
        if password.rangeOfCharacter(from: .uppercaseLetters) != nil { strength += 1 }
        return strength
    }
    
    private var passwordStrengthText: String {
        switch passwordStrength {
        case 0...1: return "弱"
        case 2: return "中"
        case 3: return "强"
        case 4: return "很强"
        default: return "弱"
        }
    }
    
    private func passwordStrengthColor(index: Int) -> Color {
        if index < passwordStrength {
            switch passwordStrength {
            case 0...1: return .red
            case 2: return .orange
            case 3: return .blue
            case 4: return .green
            default: return .gray
            }
        } else {
            return Color(hex: "eaeff1")
        }
    }
    
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
    
    private func register() {
        guard canRegister else { return }
        
        isLoading = true
        
        Task {
            do {
                await authStore.register(username: username, password: password, email: email)
                
                await MainActor.run {
                    isLoading = false
                    if authStore.isLoggedIn {
                        dismiss()
                    } else {
                        alertMessage = authStore.errorMessage ?? "注册失败，请重试"
                        showingAlert = true
                    }
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    alertMessage = error.localizedDescription
                    showingAlert = true
                }
            }
        }
    }
}

#Preview {
    RegisterView()
        .environmentObject(AuthStore())
}
