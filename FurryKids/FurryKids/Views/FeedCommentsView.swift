import SwiftUI
import Combine

struct FeedCommentsView: View {
    let feed: Feed
    @EnvironmentObject var feedStore: FeedStore
    @EnvironmentObject var authService: AuthService
    @StateObject private var commentService = CommentService()
    @State private var comments: [FeedComment] = []
    @State private var newCommentText = ""
    @State private var isLoading = false
    @State private var isSubmitting = false
    @State private var error: String?
    @State private var currentPage = 1
    @State private var hasMoreData = true
    @State private var totalComments = 0
    @Environment(\.presentationMode) var presentationMode
    @State private var cancellables = Set<AnyCancellable>()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 动态内容预览
                feedPreview
                
                Divider()
                
                // 评论列表
                if isLoading && comments.isEmpty {
                    ProgressView("加载评论中...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if comments.isEmpty {
                    emptyCommentsView
                } else {
                    commentsList
                }
                
                // 输入框
                commentInputView
            }
            .navigationTitle("评论")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("关闭") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
        .onAppear {
            loadComments()
        }
    }
    
    private var feedPreview: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(feed.petAvatar ?? feed.pet?.avatar ?? "🐕")
                    .font(.title2)
                    .frame(width: 32, height: 32)
                    .background(Color(hex: "eaeff1").opacity(0.5))
                    .clipShape(Circle())
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(feed.petName ?? feed.pet?.name ?? "未知宠物")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    Text(timeString(feed.createdAt))
                        .font(.system(size: 12))
                        .foregroundColor(Color(hex: "5c7d8a"))
                }
                
                Spacer()
            }
            
            Text(feed.content)
                .font(.system(size: 14))
                .foregroundColor(Color(hex: "101618"))
                .lineLimit(3)
        }
        .padding(16)
        .background(Color(hex: "f8f9fa"))
    }
    
    private var emptyCommentsView: some View {
        VStack(spacing: 16) {
            Image(systemName: "bubble.left")
                .font(.system(size: 48))
                .foregroundColor(Color(hex: "5c7d8a").opacity(0.5))
            
            Text("还没有评论")
                .font(.system(size: 16))
                .foregroundColor(Color(hex: "5c7d8a"))
            
            Text("成为第一个评论的人吧！")
                .font(.system(size: 14))
                .foregroundColor(Color(hex: "5c7d8a").opacity(0.7))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var commentsList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(comments) { comment in
                    CommentRow(comment: comment) { commentId in
                        deleteComment(commentId)
                    }
                    .environmentObject(authService)
                }

                // 加载更多按钮
                if hasMoreData && !comments.isEmpty {
                    Button("加载更多评论") {
                        loadComments()
                    }
                    .font(.system(size: 14))
                    .foregroundColor(Color(hex: "5c7d8a"))
                    .padding(.vertical, 8)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
        }
    }
    
    private var commentInputView: some View {
        VStack(spacing: 0) {
            Divider()
            
            HStack(spacing: 12) {
                TextField("写评论...", text: $newCommentText)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(4)
                
                Button(action: {
                    submitComment()
                }) {
                    if isSubmitting {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Text("发送")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(newCommentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? Color(hex: "5c7d8a") : Color(hex: "101618"))
                    }
                }
                .disabled(newCommentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isSubmitting)
            }
            .padding(16)
        }
        .background(Color.white)
    }
    
    private func loadComments() {
        guard !isLoading else { return }

        print("📥 FeedCommentsView: 开始加载评论 - feedId: \(feed.id), page: \(currentPage)")
        isLoading = true
        error = nil

        commentService.getComments(feedId: feed.id, page: currentPage)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    print("📥 FeedCommentsView: 评论加载完成")
                    self.isLoading = false
                    if case .failure(let error) = completion {
                        print("❌ FeedCommentsView: 评论加载失败 - \(error)")
                        self.error = error.localizedDescription
                    }
                },
                receiveValue: { response in
                    print("✅ FeedCommentsView: 评论加载成功 - 获得 \(response.comments.count) 条评论")
                    if self.currentPage == 1 {
                        self.comments = response.comments
                        print("🔄 FeedCommentsView: 重置评论列表，共 \(self.comments.count) 条")
                    } else {
                        self.comments.append(contentsOf: response.comments)
                        print("➕ FeedCommentsView: 追加评论，总共 \(self.comments.count) 条")
                    }

                    self.totalComments = response.total
                    self.hasMoreData = response.hasMore
                    self.currentPage += 1

                    print("📊 FeedCommentsView: 总评论数: \(self.totalComments), 当前页: \(self.currentPage)")
                }
            )
            .store(in: &cancellables)
    }
    
    private func submitComment() {
        let commentText = newCommentText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !commentText.isEmpty, !isSubmitting else { return }

        print("💬 FeedCommentsView: 开始提交评论 - feedId: \(feed.id), content: \(commentText)")
        isSubmitting = true

        commentService.createComment(feedId: feed.id, content: commentText)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    print("💬 FeedCommentsView: 评论提交完成")
                    self.isSubmitting = false
                    if case .failure(let error) = completion {
                        print("❌ FeedCommentsView: 评论提交失败 - \(error)")
                        self.error = error.localizedDescription
                    }
                },
                receiveValue: { newComment in
                    print("✅ FeedCommentsView: 评论提交成功 - commentId: \(newComment.id)")
                    print("📝 FeedCommentsView: 新评论内容: \(newComment.content)")
                    print("📊 FeedCommentsView: 当前评论数: \(self.comments.count)")

                    // 清空输入框
                    self.newCommentText = ""

                    // 更新Feed的评论数量
                    self.feedStore.updateFeedCommentsCount(feedId: self.feed.id, count: self.totalComments + 1)

                    // 重新加载评论列表以确保数据同步
                    self.currentPage = 1
                    self.loadComments()

                    print("🔄 FeedCommentsView: 重新加载评论列表")
                }
            )
            .store(in: &cancellables)
    }

    private func deleteComment(_ commentId: Int) {
        print("🗑️ FeedCommentsView: 开始删除评论 - commentId: \(commentId)")
        commentService.deleteComment(commentId: commentId)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    print("🗑️ FeedCommentsView: 删除评论完成")
                    if case .failure(let error) = completion {
                        print("❌ FeedCommentsView: 删除评论失败 - \(error)")
                        self.error = error.localizedDescription
                    }
                },
                receiveValue: { success in
                    print("🗑️ FeedCommentsView: 删除评论结果 - success: \(success)")
                    if success {
                        print("📊 FeedCommentsView: 删除前评论数: \(self.comments.count)")
                        // 从列表中移除评论
                        self.comments.removeAll { $0.id == commentId }
                        self.totalComments -= 1
                        print("📊 FeedCommentsView: 删除后评论数: \(self.comments.count)")

                        // 更新Feed的评论数量
                        self.feedStore.updateFeedCommentsCount(feedId: self.feed.id, count: self.totalComments)
                        print("🔄 FeedCommentsView: 已更新Feed评论数量")
                    } else {
                        print("❌ FeedCommentsView: 删除评论失败")
                        self.error = "删除评论失败"
                    }
                }
            )
            .store(in: &cancellables)
    }

    private func timeString(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter.string(from: date)
    }
}

struct CommentRow: View {
    let comment: FeedComment
    @EnvironmentObject var authService: AuthService
    @State private var showingDeleteAlert = false
    let onDelete: ((Int) -> Void)?

    init(comment: FeedComment, onDelete: ((Int) -> Void)? = nil) {
        self.comment = comment
        self.onDelete = onDelete
    }

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 用户头像
            Text(comment.userAvatar ?? comment.user?.avatar ?? "👤")
                .font(.title3)
                .frame(width: 32, height: 32)
                .background(Color(hex: "eaeff1").opacity(0.5))
                .clipShape(Circle())

            VStack(alignment: .leading, spacing: 4) {
                // 用户名和时间
                HStack {
                    Text(comment.userName ?? comment.user?.nickname ?? comment.user?.username ?? "用户")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))

                    Spacer()

                    Text(timeString(comment.createdAt))
                        .font(.system(size: 12))
                        .foregroundColor(Color(hex: "5c7d8a"))

                    // 删除按钮（仅对自己的评论显示）
                    if authService.currentUser?.id == comment.userId {
                        Button(action: {
                            showingDeleteAlert = true
                        }) {
                            Image(systemName: "trash")
                                .font(.system(size: 12))
                                .foregroundColor(Color(hex: "5c7d8a"))
                        }
                        .alert("删除评论", isPresented: $showingDeleteAlert) {
                            Button("取消", role: .cancel) { }
                            Button("删除", role: .destructive) {
                                onDelete?(comment.id)
                            }
                        } message: {
                            Text("确定要删除这条评论吗？")
                        }
                    }
                }

                // 评论内容
                Text(comment.content)
                    .font(.system(size: 14))
                    .foregroundColor(Color(hex: "101618"))
                    .fixedSize(horizontal: false, vertical: true)
            }

            Spacer()
        }
        .padding(.vertical, 8)
    }
    
    private func timeString(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter.string(from: date)
    }
}

#Preview {
    FeedCommentsView(feed: Feed.sampleData.first!)
        .environmentObject(FeedStore())
        .environmentObject(AuthService.shared)
}
