import SwiftUI
import Combine

struct FeedCommentsView: View {
    let feed: Feed
    @EnvironmentObject var feedStore: FeedStore
    @EnvironmentObject var authService: AuthService
    @State private var comments: [FeedComment] = []
    @State private var newCommentText = ""
    @State private var isLoading = false
    @State private var error: String?
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 动态内容预览
                feedPreview
                
                Divider()
                
                // 评论列表
                if isLoading && comments.isEmpty {
                    ProgressView("加载评论中...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if comments.isEmpty {
                    emptyCommentsView
                } else {
                    commentsList
                }
                
                // 输入框
                commentInputView
            }
            .navigationTitle("评论")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("关闭") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
        .onAppear {
            loadComments()
        }
    }
    
    private var feedPreview: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(feed.petAvatar ?? feed.pet?.avatar ?? "🐕")
                    .font(.title2)
                    .frame(width: 32, height: 32)
                    .background(Color(hex: "eaeff1").opacity(0.5))
                    .clipShape(Circle())
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(feed.petName ?? feed.pet?.name ?? "未知宠物")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    Text(timeString(feed.createdAt))
                        .font(.system(size: 12))
                        .foregroundColor(Color(hex: "5c7d8a"))
                }
                
                Spacer()
            }
            
            Text(feed.content)
                .font(.system(size: 14))
                .foregroundColor(Color(hex: "101618"))
                .lineLimit(3)
        }
        .padding(16)
        .background(Color(hex: "f8f9fa"))
    }
    
    private var emptyCommentsView: some View {
        VStack(spacing: 16) {
            Image(systemName: "bubble.left")
                .font(.system(size: 48))
                .foregroundColor(Color(hex: "5c7d8a").opacity(0.5))
            
            Text("还没有评论")
                .font(.system(size: 16))
                .foregroundColor(Color(hex: "5c7d8a"))
            
            Text("成为第一个评论的人吧！")
                .font(.system(size: 14))
                .foregroundColor(Color(hex: "5c7d8a").opacity(0.7))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var commentsList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(comments) { comment in
                    CommentRow(comment: comment)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
        }
    }
    
    private var commentInputView: some View {
        VStack(spacing: 0) {
            Divider()
            
            HStack(spacing: 12) {
                TextField("写评论...", text: $newCommentText, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(1...4)
                
                Button(action: {
                    submitComment()
                }) {
                    Text("发送")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(newCommentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? Color(hex: "5c7d8a") : Color(hex: "101618"))
                }
                .disabled(newCommentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            }
            .padding(16)
        }
        .background(Color.white)
    }
    
    private func loadComments() {
        // TODO: 实现加载评论功能
        isLoading = true
        
        // 模拟API调用
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            self.isLoading = false
            // 这里应该调用真实的API
        }
    }
    
    private func submitComment() {
        let commentText = newCommentText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !commentText.isEmpty else { return }
        
        // TODO: 实现提交评论功能
        print("提交评论: \(commentText)")
        
        // 清空输入框
        newCommentText = ""
    }
    
    private func timeString(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter.string(from: date)
    }
}

struct CommentRow: View {
    let comment: FeedComment
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 用户头像
            Text("👤")
                .font(.title3)
                .frame(width: 32, height: 32)
                .background(Color(hex: "eaeff1").opacity(0.5))
                .clipShape(Circle())
            
            VStack(alignment: .leading, spacing: 4) {
                // 用户名和时间
                HStack {
                    Text(comment.userName ?? "用户")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    Spacer()
                    
                    Text(timeString(comment.createdAt))
                        .font(.system(size: 12))
                        .foregroundColor(Color(hex: "5c7d8a"))
                }
                
                // 评论内容
                Text(comment.content)
                    .font(.system(size: 14))
                    .foregroundColor(Color(hex: "101618"))
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            Spacer()
        }
        .padding(.vertical, 8)
    }
    
    private func timeString(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter.string(from: date)
    }
}

#Preview {
    FeedCommentsView(feed: Feed.sampleData.first!)
        .environmentObject(FeedStore())
        .environmentObject(AuthService.shared)
}
