import SwiftUI

struct FeedView: View {
    @EnvironmentObject var feedStore: FeedStore
    @Binding var selectedTab: Int  // 添加selectedTab绑定
    @State private var showingCreateFeed = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部导航栏
                HStack {
                    Text("毛孩圈")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(Color(hex: "101618"))
                    
                    Spacer()
                    
                    Button(action: {
                        showingCreateFeed = true
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(Color(hex: "101618"))
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 12)
                .padding(.bottom, 8)
                
                // 动态列表
                ScrollView {
                    LazyVStack(spacing: 16) {
                        ForEach(feedStore.feeds) { feed in
                            FeedCard(feed: feed)
                                .environmentObject(AuthService.shared)
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 8)
                    .padding(.bottom, 80)
                }
                .background(Color(hex: "F9FAFB"))
            }
            .background(Color(hex: "F9FAFB"))
            .navigationBarHidden(true)
            .sheet(isPresented: $showingCreateFeed) {
                CreateFeedView(selectedTab: $selectedTab)
                    .environmentObject(feedStore)
            }
        }
    }
}

struct FeedCard: View {
    let feed: Feed
    @EnvironmentObject var feedStore: FeedStore
    @EnvironmentObject var authService: AuthService
    @State private var isLiked: Bool
    @State private var likesCount: Int
    @State private var showingActionSheet = false
    @State private var showingComments = false
    @State private var showingEditFeed = false
    @State private var showingDeleteAlert = false

    init(feed: Feed) {
        self.feed = feed
        self._isLiked = State(initialValue: feed.isLiked)
        self._likesCount = State(initialValue: feed.likesCount)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 宠物信息和时间
            HStack {
                // 宠物头像
                Text(feed.petAvatar ?? feed.pet?.avatar ?? "🐕")
                    .font(.title)
                    .frame(width: 40, height: 40)
                    .background(Color(hex: "eaeff1").opacity(0.5))
                    .clipShape(Circle())

                VStack(alignment: .leading, spacing: 2) {
                    // 宠物名称 - 优先使用后端返回的petName
                    Text(feed.petName ?? feed.pet?.name ?? "未知宠物")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(Color(hex: "101618"))
                    
                    // 发布时间
                    Text(timeString(feed.createdAt))
                        .font(.system(size: 12))
                        .foregroundColor(Color(hex: "5c7d8a"))
                }
                
                Spacer()
                
                // 更多选项按钮
                Button(action: {
                    showingActionSheet = true
                }) {
                    Image(systemName: "ellipsis")
                        .font(.system(size: 20))
                        .foregroundColor(Color(hex: "5c7d8a"))
                }
                .actionSheet(isPresented: $showingActionSheet) {
                    ActionSheet(
                        title: Text("动态操作"),
                        buttons: actionSheetButtons()
                    )
                }
            }
            
            // 动态内容
            Text(feed.content)
                .font(.system(size: 16))
                .foregroundColor(Color(hex: "101618"))
                .multilineTextAlignment(.leading)
                .fixedSize(horizontal: false, vertical: true)
            
            // 图片
            if !feed.images.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(feed.images, id: \.self) { imageUrl in
                            AsyncImage(url: URL(string: "\(APIConfig.baseURL)\(imageUrl)")) { image in
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: 200, height: 150)
                                    .clipped()
                                    .cornerRadius(12)
                            } placeholder: {
                                Rectangle()
                                    .fill(Color.gray.opacity(0.2))
                                    .frame(width: 200, height: 150)
                                    .cornerRadius(12)
                                    .overlay(
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle())
                                    )
                            }
                        }
                    }
                }
                .padding(.horizontal, 16)
            }
            
            // 互动按钮
            HStack(spacing: 16) {
                // 点赞按钮
                Button(action: {
                    toggleLike()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: isLiked ? "heart.fill" : "heart")
                            .font(.system(size: 16))
                            .foregroundColor(isLiked ? .red : Color(hex: "5c7d8a"))

                        Text("\(likesCount)")
                            .font(.system(size: 14))
                            .foregroundColor(Color(hex: "5c7d8a"))
                    }
                }

                // 评论按钮
                Button(action: {
                    showingComments = true
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "bubble.left")
                            .font(.system(size: 16))
                            .foregroundColor(Color(hex: "5c7d8a"))

                        Text("\(feed.commentsCount)")
                            .font(.system(size: 14))
                            .foregroundColor(Color(hex: "5c7d8a"))
                    }
                }
                
                // 分享按钮
                Button(action: {
                    // 分享功能
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "square.and.arrow.up")
                            .font(.system(size: 16))
                            .foregroundColor(Color(hex: "5c7d8a"))
                    }
                }
                
                Spacer()
            }
        }
        .padding(16)
        .background(Color.white)
        .cornerRadius(16)
        .sheet(isPresented: $showingComments) {
            FeedCommentsView(feed: feed)
                .environmentObject(feedStore)
                .environmentObject(AuthService.shared)
        }
        .sheet(isPresented: $showingEditFeed) {
            EditFeedView(feed: feed)
                .environmentObject(feedStore)
        }
        .alert("删除动态", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                feedStore.deleteFeed(feed.id)
            }
        } message: {
            Text("确定要删除这条动态吗？删除后无法恢复。")
        }
    }

    private func timeString(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter.string(from: date)
    }

    private func actionSheetButtons() -> [ActionSheet.Button] {
        var buttons: [ActionSheet.Button] = []

        // 如果是当前用户的动态，显示编辑和删除选项
        if authService.currentUser?.id == feed.userId {
            buttons.append(.default(Text("编辑")) {
                showingEditFeed = true
            })

            buttons.append(.destructive(Text("删除")) {
                showingDeleteAlert = true
            })
        }

        buttons.append(.cancel())
        return buttons
    }

    private func toggleLike() {
        let previousState = isLiked
        let previousCount = likesCount

        // 乐观更新UI
        isLiked.toggle()
        likesCount += isLiked ? 1 : -1

        // 调用API
        feedStore.toggleLike(feedId: feed.id) { success in
            if !success {
                // 如果失败，恢复之前的状态
                DispatchQueue.main.async {
                    self.isLiked = previousState
                    self.likesCount = previousCount
                }
            }
        }
    }
}

#Preview {
    FeedView(selectedTab: .constant(0))
        .environmentObject(FeedStore())
}