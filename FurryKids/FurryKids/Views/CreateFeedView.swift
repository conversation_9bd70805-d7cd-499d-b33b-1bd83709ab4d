import SwiftUI
import Combine

struct CreateFeedView: View {
    @EnvironmentObject var feedStore: FeedStore
    @EnvironmentObject var petStore: PetStore
    @Environment(\.dismiss) private var dismiss
    @Binding var selectedTab: Int  // 添加这个绑定来控制tab切换

    @State private var content: String = ""
    @State private var selectedImages: [UIImage] = []
    @State private var showingImagePicker = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var showNoPetAlert = false  // 专门用于没有宠物的alert
    @State private var showingPetSelector = false  // 显示宠物选择器
    @State private var selectedPet: Pet?  // 当前选择的宠物
    @State private var isUploading = false  // 上传状态
    @State private var cancellables = Set<AnyCancellable>()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 内容输入区域
                VStack(alignment: .leading, spacing: 16) {
                    // 宠物信息和选择
                    if petStore.pets.isEmpty {
                        // 没有宠物时的提示
                        VStack(alignment: .leading, spacing: 8) {
                            Text("当存在多个宠物时，")
                                .foregroundColor(.red)
                                .font(.system(size: 14))
                            Text("此处的宠物需要能够选择")
                                .foregroundColor(.red)
                                .font(.system(size: 14))
                        }
                    } else if petStore.pets.count == 1 {
                        // 只有一个宠物时直接显示
                        HStack(spacing: 12) {
                            Text(petStore.pets.first?.avatar ?? "🐕")
                                .font(.title)
                                .frame(width: 40, height: 40)
                                .background(Color(hex: "eaeff1").opacity(0.5))
                                .clipShape(Circle())

                            Text(petStore.pets.first?.name ?? "我的宠物")
                                .font(.system(size: 16, weight: .bold))
                                .foregroundColor(Color(hex: "101618"))
                        }
                    } else {
                        // 多个宠物时显示选择器
                        Button(action: {
                            showingPetSelector = true
                        }) {
                            HStack(spacing: 12) {
                                Text(selectedPet?.avatar ?? petStore.pets.first?.avatar ?? "🐕")
                                    .font(.title)
                                    .frame(width: 40, height: 40)
                                    .background(Color(hex: "eaeff1").opacity(0.5))
                                    .clipShape(Circle())

                                VStack(alignment: .leading, spacing: 2) {
                                    Text(selectedPet?.name ?? petStore.pets.first?.name ?? "选择宠物")
                                        .font(.system(size: 16, weight: .bold))
                                        .foregroundColor(Color(hex: "101618"))

                                    Text("点击选择其他宠物")
                                        .font(.system(size: 12))
                                        .foregroundColor(Color(hex: "5c7d8a"))
                                }

                                Spacer()

                                Image(systemName: "chevron.down")
                                    .font(.system(size: 12))
                                    .foregroundColor(Color(hex: "5c7d8a"))
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    
                    // 文本输入
                    TextEditor(text: $content)
                        .placeholder(when: content.isEmpty) {
                            Text("分享你家毛孩子的日常...")
                                .foregroundColor(Color(hex: "5c7d8a").opacity(0.5))
                                .padding(.top, 8)
                                .padding(.leading, 5)
                        }
                        .font(.system(size: 16))
                        .foregroundColor(Color(hex: "101618"))
                        .frame(minHeight: 120)
                        .padding(8)
                        .background(Color(hex: "F9FAFB"))
                        .cornerRadius(12)
                    
                    // 已选图片预览
                    if !selectedImages.isEmpty {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 8) {
                                ForEach(Array(selectedImages.enumerated()), id: \.offset) { index, image in
                                    ZStack(alignment: .topTrailing) {
                                        // 显示真实图片
                                        Image(uiImage: image)
                                            .resizable()
                                            .aspectRatio(contentMode: .fill)
                                            .frame(width: 100, height: 100)
                                            .clipped()
                                            .cornerRadius(8)

                                        // 删除按钮
                                        Button(action: {
                                            selectedImages.remove(at: index)
                                        }) {
                                            Image(systemName: "xmark.circle.fill")
                                                .foregroundColor(.white)
                                                .background(Color.black.opacity(0.6))
                                                .clipShape(Circle())
                                        }
                                        .padding(4)
                                    }
                                }
                            }
                            .padding(.horizontal, 4)
                        }
                    }
                    
                    // 功能按钮
                    HStack(spacing: 16) {
                        // 添加图片 - 使用传统的ImagePicker
                        Button(action: {
                            showingImagePicker = true
                        }) {
                            HStack(spacing: 8) {
                                Image(systemName: "photo")
                                    .font(.system(size: 20))
                                    .foregroundColor(Color(hex: "5c7d8a"))

                                Text("添加图片")
                                    .font(.system(size: 14))
                                    .foregroundColor(Color(hex: "5c7d8a"))
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color(hex: "eaeff1"))
                            .cornerRadius(16)
                        }
                        
                        Spacer()
                        
                        // 发布按钮
                        Button(action: publishFeed) {
                            HStack(spacing: 8) {
                                if isUploading {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                }
                                Text(isUploading ? "发布中..." : "发布")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 10)
                            .background(
                                (content.isEmpty || isUploading) ? Color(hex: "5c7d8a").opacity(0.5) : Color(hex: "5c7d8a")
                            )
                            .cornerRadius(20)
                        }
                        .disabled(content.isEmpty || isUploading)
                    }
                }
                .padding(16)
                
                Spacer()
            }
            .navigationTitle("创建动态")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
        .alert("提示", isPresented: $showAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .alert("需要添加宠物", isPresented: $showNoPetAlert) {
            Button("去添加宠物") {
                selectedTab = 1  // 切换到档案页面
                dismiss()  // 关闭当前页面
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("您还没有添加宠物信息，请先去「档案」页面添加您的宠物，然后再来发布动态吧！")
        }
        .sheet(isPresented: $showingPetSelector) {
            PetSelectorView(selectedPet: $selectedPet, pets: petStore.pets)
        }
        .sheet(isPresented: $showingImagePicker) {
            ImagePicker(selectedImages: $selectedImages)
        }
        .onAppear {
            // 初始化选择的宠物
            if selectedPet == nil {
                selectedPet = petStore.pets.first
            }
        }
    }
    
    private func publishFeed() {
        print("🔥 publishFeed 函数被调用了！")

        // 确定要使用的宠物
        let targetPet: Pet?
        if petStore.pets.isEmpty {
            showNoPetAlert = true
            return
        } else if petStore.pets.count == 1 {
            targetPet = petStore.pets.first
        } else {
            targetPet = selectedPet ?? petStore.pets.first
        }

        guard let pet = targetPet else {
            print("❌ 发布失败：没有选择宠物")
            showNoPetAlert = true
            return
        }

        print("🚀 开始发布动态...")
        print("📝 内容: \(content)")
        print("🐾 宠物ID: \(pet.id)")
        print("🐾 宠物名称: \(pet.name)")
        print("🖼️ 图片数量: \(selectedImages.count)")

        isUploading = true

        // 如果有图片，先上传图片
        if !selectedImages.isEmpty {
            uploadImagesAndCreateFeed(pet: pet)
        } else {
            // 没有图片，直接创建动态
            createFeedWithImages(pet: pet, imageUrls: [])
        }
    }

    private func uploadImagesAndCreateFeed(pet: Pet) {
        let imageUploadService = ImageUploadService.shared
        var uploadedUrls: [String] = []
        var uploadCount = 0
        let totalImages = selectedImages.count

        for image in selectedImages {
            imageUploadService.uploadFeedImage(image)
                .receive(on: DispatchQueue.main)
                .sink(
                    receiveCompletion: { [weak self] completion in
                        if case .failure(let error) = completion {
                            print("❌ 图片上传失败: \(error)")
                            self?.isUploading = false
                            self?.alertMessage = "图片上传失败，请重试"
                            self?.showAlert = true
                        }
                    },
                    receiveValue: { [weak self] response in
                        uploadedUrls.append(response.url)
                        uploadCount += 1

                        print("✅ 图片上传成功: \(response.url)")
                        print("📊 上传进度: \(uploadCount)/\(totalImages)")

                        // 所有图片都上传完成
                        if uploadCount == totalImages {
                            self?.createFeedWithImages(pet: pet, imageUrls: uploadedUrls)
                        }
                    }
                )
                .store(in: &cancellables)
        }
    }

    private func createFeedWithImages(pet: Pet, imageUrls: [String]) {
        print("🚀 创建动态，图片URLs: \(imageUrls)")

        feedStore.createFeed(
            content: content,
            images: imageUrls,
            petId: pet.id
        )

        isUploading = false
        dismiss()
    }
}

// MARK: - 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImages: [UIImage]
    @Environment(\.dismiss) private var dismiss

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        picker.allowsEditing = false
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImages.append(image)
            }
            parent.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}

// MARK: - 宠物选择器视图
struct PetSelectorView: View {
    @Binding var selectedPet: Pet?
    let pets: [Pet]
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List {
                ForEach(pets) { pet in
                    Button(action: {
                        selectedPet = pet
                        dismiss()
                    }) {
                        HStack(spacing: 12) {
                            Text(pet.avatar ?? "🐕")
                                .font(.title2)
                                .frame(width: 40, height: 40)
                                .background(Color(hex: "eaeff1").opacity(0.5))
                                .clipShape(Circle())

                            VStack(alignment: .leading, spacing: 4) {
                                Text(pet.name)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(Color(hex: "101618"))

                                if !pet.breed.isEmpty {
                                    Text(pet.breed)
                                        .font(.system(size: 14))
                                        .foregroundColor(Color(hex: "5c7d8a"))
                                }
                            }

                            Spacer()

                            if let selectedPet = selectedPet, selectedPet.id == pet.id {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(Color(hex: "5c7d8a"))
                            }
                        }
                        .padding(.vertical, 4)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .navigationTitle("选择宠物")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// 文本编辑器占位符扩展
extension View {
    func placeholder<Content: View>(
        when shouldShow: Bool,
        alignment: Alignment = .leading,
        @ViewBuilder placeholder: () -> Content
    ) -> some View {
        ZStack(alignment: alignment) {
            placeholder().opacity(shouldShow ? 1 : 0)
            self
        }
    }
}

#Preview {
    CreateFeedView(selectedTab: .constant(0))
        .environmentObject(FeedStore())
        .environmentObject(PetStore())
}