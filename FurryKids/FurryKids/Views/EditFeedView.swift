import SwiftUI
import Combine

struct EditFeedView: View {
    let feed: Feed
    @EnvironmentObject var feedStore: FeedStore
    @Environment(\.dismiss) private var dismiss
    
    @State private var content: String
    @State private var mood: String
    @State private var tags: [String]
    @State private var location: String
    @State private var isPublic: Bool
    @State private var isUpdating = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    
    @State private var cancellables = Set<AnyCancellable>()
    
    init(feed: Feed) {
        self.feed = feed
        self._content = State(initialValue: feed.content)
        self._mood = State(initialValue: feed.mood ?? "")
        self._tags = State(initialValue: feed.tags)
        self._location = State(initialValue: feed.location ?? "")
        self._isPublic = State(initialValue: feed.isPublic)
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 动态内容
                    VStack(alignment: .leading, spacing: 8) {
                        Text("动态内容")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(Color(hex: "101618"))
                        
                        TextEditor(text: $content)
                            .frame(minHeight: 120)
                            .padding(12)
                            .background(Color(hex: "F9FAFB"))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color(hex: "E5E7EB"), lineWidth: 1)
                            )
                            .keyboardType(.default)  // 支持中文输入
                    }
                    
                    // 心情
                    VStack(alignment: .leading, spacing: 8) {
                        Text("心情")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(Color(hex: "101618"))
                        
                        TextField("输入心情（可选）", text: $mood)
                            .padding(12)
                            .background(Color(hex: "F9FAFB"))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color(hex: "E5E7EB"), lineWidth: 1)
                            )
                    }
                    
                    // 位置
                    VStack(alignment: .leading, spacing: 8) {
                        Text("位置")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(Color(hex: "101618"))
                        
                        TextField("输入位置（可选）", text: $location)
                            .padding(12)
                            .background(Color(hex: "F9FAFB"))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color(hex: "E5E7EB"), lineWidth: 1)
                            )
                    }
                    
                    // 标签显示
                    if !tags.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("标签")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(Color(hex: "101618"))
                            
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 8) {
                                    ForEach(tags, id: \.self) { tag in
                                        Text("#\(tag)")
                                            .font(.system(size: 14))
                                            .padding(.horizontal, 12)
                                            .padding(.vertical, 6)
                                            .background(Color(hex: "EEF2FF"))
                                            .foregroundColor(Color(hex: "4F46E5"))
                                            .cornerRadius(16)
                                    }
                                }
                                .padding(.horizontal, 16)
                            }
                        }
                    }
                    
                    // 公开设置
                    HStack {
                        Text("公开动态")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(Color(hex: "101618"))
                        
                        Spacer()
                        
                        Toggle("", isOn: $isPublic)
                            .toggleStyle(SwitchToggleStyle(tint: Color(hex: "4F46E5")))
                    }
                    
                    Spacer(minLength: 20)
                }
                .padding(16)
            }
            .background(Color(hex: "F9FAFB"))
            .navigationTitle("编辑动态")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                    .foregroundColor(Color(hex: "5c7d8a"))
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        updateFeed()
                    }
                    .foregroundColor(Color(hex: "4F46E5"))
                    .disabled(content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isUpdating)
                }
            }
            .alert("提示", isPresented: $showAlert) {
                Button("确定") { }
            } message: {
                Text(alertMessage)
            }
        }
    }
    
    private func updateFeed() {
        let trimmedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedContent.isEmpty else {
            alertMessage = "动态内容不能为空"
            showAlert = true
            return
        }
        
        isUpdating = true
        
        let updateRequest = FeedUpdateRequest(
            content: trimmedContent,
            images: feed.images, // 保持原有图片
            tags: tags,
            mood: mood.isEmpty ? nil : mood,
            location: location.isEmpty ? nil : location,
            isPublic: isPublic,
            status: nil // 保持原状态
        )
        
        feedStore.updateFeed(feedId: feed.id, request: updateRequest)
            .receive(on: DispatchQueue.main)
            .sink { success in
                self.isUpdating = false
                if success {
                    self.dismiss()
                } else {
                    self.alertMessage = "更新失败，请重试"
                    self.showAlert = true
                }
            }
            .store(in: &cancellables)
    }
}

#Preview {
    EditFeedView(feed: Feed.sampleData[0])
        .environmentObject(FeedStore())
}
