import SwiftUI

// MARK: - 原生中文输入支持的 TextField
struct NativeChineseTextField: View {
    @Binding var text: String
    var placeholder: String
    var isSecure: Bool = false
    var keyboardType: UIKeyboardType = .default
    var onCommit: (() -> Void)? = nil
    
    var body: some View {
        Group {
            if isSecure {
                SecureField(placeholder, text: $text)
                    .onSubmit {
                        onCommit?()
                    }
            } else {
                TextField(placeholder, text: $text)
                    .keyboardType(keyboardType)
                    .onSubmit {
                        onCommit?()
                    }
            }
        }
        .textFieldStyle(CustomTextFieldStyle())
        .autocorrectionDisabled(false)  // 启用自动纠错以支持中文输入法
        .textInputAutocapitalization(.never)  // 禁用自动大写
    }
}

// MARK: - 原生中文输入支持的 TextEditor
struct NativeChineseTextEditor: View {
    @Binding var text: String
    var placeholder: String = ""
    var minHeight: CGFloat = 120
    
    @State private var isEditing = false
    
    var body: some View {
        ZStack(alignment: .topLeading) {
            TextEditor(text: $text)
                .frame(minHeight: minHeight)
                .padding(8)
                .background(Color(hex: "F9FAFB"))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(hex: "E5E7EB"), lineWidth: 1)
                )
                .autocorrectionDisabled(false)  // 启用自动纠错以支持中文输入法
                .textInputAutocapitalization(.never)  // 禁用自动大写
                .onTapGesture {
                    isEditing = true
                }
            
            if text.isEmpty && !isEditing {
                Text(placeholder)
                    .foregroundColor(Color(hex: "5c7d8a").opacity(0.5))
                    .padding(.top, 16)
                    .padding(.leading, 13)
                    .allowsHitTesting(false)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: UITextView.textDidBeginEditingNotification)) { _ in
            isEditing = true
        }
        .onReceive(NotificationCenter.default.publisher(for: UITextView.textDidEndEditingNotification)) { _ in
            isEditing = false
        }
    }
}

// MARK: - 自定义文本框样式
struct CustomTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(12)
            .background(Color(hex: "F9FAFB"))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(hex: "E5E7EB"), lineWidth: 1)
            )
            .font(.system(size: 16))
            .foregroundColor(Color(hex: "101618"))
    }
}

// MARK: - 颜色扩展
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - 预览
struct NativeChineseTextField_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            NativeChineseTextField(
                text: .constant(""),
                placeholder: "请输入宠物名称"
            )
            .frame(height: 48)
            
            NativeChineseTextField(
                text: .constant(""),
                placeholder: "请输入密码",
                isSecure: true
            )
            .frame(height: 48)
            
            NativeChineseTextEditor(
                text: .constant(""),
                placeholder: "请输入描述..."
            )
            .frame(height: 120)
        }
        .padding()
    }
}
