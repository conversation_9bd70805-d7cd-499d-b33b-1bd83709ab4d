import SwiftUI
import UIKit

// MARK: - 简化的中文输入支持 TextField
struct SimpleChineseTextField: UIViewRepresentable {
    @Binding var text: String
    var placeholder: String
    var isSecure: Bool = false
    var keyboardType: UIKeyboardType = .default
    var returnKeyType: UIReturnKeyType = .default
    var onCommit: (() -> Void)?
    
    func makeUIView(context: Context) -> UITextField {
        let textField = UITextField()
        
        // 基本配置
        textField.placeholder = placeholder
        textField.text = text
        textField.delegate = context.coordinator
        textField.isSecureTextEntry = isSecure
        textField.keyboardType = keyboardType
        textField.returnKeyType = returnKeyType
        
        // 中文输入支持 - 关键配置
        textField.autocapitalizationType = .none
        textField.autocorrectionType = .no  // 关闭自动纠错
        textField.spellCheckingType = .no   // 关闭拼写检查
        
        // 外观配置
        textField.borderStyle = .none
        textField.backgroundColor = UIColor(red: 0.976, green: 0.980, blue: 0.984, alpha: 1.0)
        textField.layer.cornerRadius = 8
        textField.layer.borderWidth = 1
        textField.layer.borderColor = UIColor(red: 0.898, green: 0.906, blue: 0.922, alpha: 1.0).cgColor
        
        // 内边距
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        textField.rightViewMode = .always
        
        // 字体和颜色
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.textColor = UIColor(red: 0.063, green: 0.086, blue: 0.094, alpha: 1.0)
        
        // 占位符颜色
        textField.attributedPlaceholder = NSAttributedString(
            string: placeholder,
            attributes: [
                .foregroundColor: UIColor(red: 0.361, green: 0.490, blue: 0.541, alpha: 0.5)
            ]
        )
        
        // 监听文本变化
        textField.addTarget(context.coordinator, action: #selector(context.coordinator.textDidChange), for: .editingChanged)
        
        return textField
    }
    
    func updateUIView(_ uiView: UITextField, context: Context) {
        if uiView.text != text {
            uiView.text = text
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UITextFieldDelegate {
        let parent: SimpleChineseTextField
        
        init(_ parent: SimpleChineseTextField) {
            self.parent = parent
        }
        
        @objc func textDidChange(_ textField: UITextField) {
            // 直接同步文本，不做任何过滤
            DispatchQueue.main.async {
                self.parent.text = textField.text ?? ""
            }
        }
        
        func textFieldDidEndEditing(_ textField: UITextField) {
            parent.text = textField.text ?? ""
        }
        
        func textFieldShouldReturn(_ textField: UITextField) -> Bool {
            parent.onCommit?()
            return true
        }
    }
}

// MARK: - 简化的中文输入支持 TextEditor
struct SimpleChineseTextEditor: UIViewRepresentable {
    @Binding var text: String
    var placeholder: String = ""
    var font: UIFont = UIFont.systemFont(ofSize: 16)
    var textColor: UIColor = UIColor(red: 0.063, green: 0.086, blue: 0.094, alpha: 1.0)
    var backgroundColor: UIColor = UIColor(red: 0.976, green: 0.980, blue: 0.984, alpha: 1.0)
    
    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        
        // 基本配置
        textView.delegate = context.coordinator
        textView.font = font
        textView.backgroundColor = backgroundColor
        textView.textColor = textColor
        textView.text = text.isEmpty ? placeholder : text
        
        // 中文输入支持 - 关键配置
        textView.autocapitalizationType = .none
        textView.autocorrectionType = .no  // 关闭自动纠错
        textView.spellCheckingType = .no   // 关闭拼写检查
        
        // 外观配置
        textView.layer.cornerRadius = 12
        textView.layer.borderWidth = 1
        textView.layer.borderColor = UIColor(red: 0.898, green: 0.906, blue: 0.922, alpha: 1.0).cgColor
        textView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        
        // 占位符处理
        if text.isEmpty {
            textView.text = placeholder
            textView.textColor = UIColor(red: 0.361, green: 0.490, blue: 0.541, alpha: 0.5)
        }
        
        return textView
    }
    
    func updateUIView(_ uiView: UITextView, context: Context) {
        if uiView.text != text {
            if text.isEmpty {
                uiView.text = placeholder
                uiView.textColor = UIColor(red: 0.361, green: 0.490, blue: 0.541, alpha: 0.5)
            } else {
                uiView.text = text
                uiView.textColor = textColor
            }
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UITextViewDelegate {
        let parent: SimpleChineseTextEditor
        
        init(_ parent: SimpleChineseTextEditor) {
            self.parent = parent
        }
        
        func textViewDidBeginEditing(_ textView: UITextView) {
            if textView.text == parent.placeholder {
                textView.text = ""
                textView.textColor = parent.textColor
            }
        }
        
        func textViewDidEndEditing(_ textView: UITextView) {
            if textView.text.isEmpty {
                textView.text = parent.placeholder
                textView.textColor = UIColor(red: 0.361, green: 0.490, blue: 0.541, alpha: 0.5)
            }
            parent.text = textView.text == parent.placeholder ? "" : textView.text
        }
        
        func textViewDidChange(_ textView: UITextView) {
            // 直接同步文本，不做任何过滤
            if textView.text != parent.placeholder {
                DispatchQueue.main.async {
                    self.parent.text = textView.text
                }
            }
        }
    }
}

// MARK: - 预览
struct SimpleChineseTextField_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            SimpleChineseTextField(
                text: .constant(""),
                placeholder: "请输入宠物名称"
            )
            .frame(height: 48)
            
            SimpleChineseTextEditor(
                text: .constant(""),
                placeholder: "请输入描述..."
            )
            .frame(height: 120)
        }
        .padding()
    }
}
