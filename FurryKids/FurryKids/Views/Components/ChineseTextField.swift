import SwiftUI
import UIKit

// MARK: - 支持中文输入的 TextField
struct ChineseTextField: UIViewRepresentable {
    @Binding var text: String
    var placeholder: String
    var isSecure: Bool = false
    var keyboardType: UIKeyboardType = .default
    var returnKeyType: UIReturnKeyType = .default
    var onCommit: (() -> Void)?
    
    func makeUIView(context: Context) -> UITextField {
        let textField = UITextField()
        
        // 基本配置
        textField.placeholder = placeholder
        textField.text = text
        textField.delegate = context.coordinator
        textField.isSecureTextEntry = isSecure
        textField.keyboardType = keyboardType
        textField.returnKeyType = returnKeyType
        
        // 中文输入支持配置
        textField.autocapitalizationType = .none
        textField.autocorrectionType = .yes  // 启用自动纠错以支持中文输入法
        textField.spellCheckingType = .yes   // 启用拼写检查
        textField.smartDashesType = .yes     // 启用智能破折号
        textField.smartQuotesType = .yes     // 启用智能引号
        textField.smartInsertDeleteType = .yes // 启用智能插入删除
        
        // 外观配置
        textField.borderStyle = .none
        textField.backgroundColor = UIColor(red: 0.976, green: 0.980, blue: 0.984, alpha: 1.0) // #F9FAFB
        textField.layer.cornerRadius = 12
        textField.layer.borderWidth = 1
        textField.layer.borderColor = UIColor(red: 0.898, green: 0.906, blue: 0.922, alpha: 1.0).cgColor // #E5E7EB
        
        // 内边距
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.rightViewMode = .always
        
        // 字体和颜色
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.textColor = UIColor(red: 0.063, green: 0.086, blue: 0.094, alpha: 1.0) // #101618
        
        // 占位符颜色
        textField.attributedPlaceholder = NSAttributedString(
            string: placeholder,
            attributes: [
                .foregroundColor: UIColor(red: 0.361, green: 0.490, blue: 0.541, alpha: 0.5) // #5c7d8a with 50% opacity
            ]
        )
        
        return textField
    }
    
    func updateUIView(_ uiView: UITextField, context: Context) {
        if uiView.text != text {
            uiView.text = text
        }
        uiView.placeholder = placeholder
        uiView.isSecureTextEntry = isSecure
        uiView.keyboardType = keyboardType
        uiView.returnKeyType = returnKeyType
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UITextFieldDelegate {
        let parent: ChineseTextField
        
        init(_ parent: ChineseTextField) {
            self.parent = parent
        }
        
        func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
            // 获取当前文本
            let currentText = textField.text ?? ""
            
            // 计算新文本
            guard let stringRange = Range(range, in: currentText) else { return false }
            let newText = currentText.replacingCharacters(in: stringRange, with: string)
            
            // 更新绑定的文本
            DispatchQueue.main.async {
                self.parent.text = newText
            }
            
            return true
        }
        
        func textFieldDidEndEditing(_ textField: UITextField) {
            parent.text = textField.text ?? ""
        }
        
        func textFieldShouldReturn(_ textField: UITextField) -> Bool {
            parent.onCommit?()
            return true
        }
    }
}

// MARK: - 支持中文输入的 TextEditor
struct ChineseTextEditor: UIViewRepresentable {
    @Binding var text: String
    var placeholder: String = ""
    var font: UIFont = UIFont.systemFont(ofSize: 16)
    var textColor: UIColor = UIColor(red: 0.063, green: 0.086, blue: 0.094, alpha: 1.0)
    var backgroundColor: UIColor = UIColor(red: 0.976, green: 0.980, blue: 0.984, alpha: 1.0)
    
    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        
        // 基本配置
        textView.text = text
        textView.delegate = context.coordinator
        textView.font = font
        textView.textColor = textColor
        textView.backgroundColor = backgroundColor
        
        // 中文输入支持配置
        textView.autocapitalizationType = .none
        textView.autocorrectionType = .yes  // 启用自动纠错以支持中文输入法
        textView.spellCheckingType = .yes   // 启用拼写检查
        textView.smartDashesType = .yes     // 启用智能破折号
        textView.smartQuotesType = .yes     // 启用智能引号
        textView.smartInsertDeleteType = .yes // 启用智能插入删除
        
        // 外观配置
        textView.layer.cornerRadius = 12
        textView.layer.borderWidth = 1
        textView.layer.borderColor = UIColor(red: 0.898, green: 0.906, blue: 0.922, alpha: 1.0).cgColor
        textView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        
        // 占位符处理
        if text.isEmpty {
            textView.text = placeholder
            textView.textColor = UIColor(red: 0.361, green: 0.490, blue: 0.541, alpha: 0.5)
        }
        
        return textView
    }
    
    func updateUIView(_ uiView: UITextView, context: Context) {
        if uiView.text != text {
            if text.isEmpty {
                uiView.text = placeholder
                uiView.textColor = UIColor(red: 0.361, green: 0.490, blue: 0.541, alpha: 0.5)
            } else {
                uiView.text = text
                uiView.textColor = textColor
            }
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UITextViewDelegate {
        let parent: ChineseTextEditor
        
        init(_ parent: ChineseTextEditor) {
            self.parent = parent
        }
        
        func textViewDidBeginEditing(_ textView: UITextView) {
            if textView.text == parent.placeholder {
                textView.text = ""
                textView.textColor = parent.textColor
            }
        }
        
        func textViewDidEndEditing(_ textView: UITextView) {
            if textView.text.isEmpty {
                textView.text = parent.placeholder
                textView.textColor = UIColor(red: 0.361, green: 0.490, blue: 0.541, alpha: 0.5)
            }
            parent.text = textView.text == parent.placeholder ? "" : textView.text
        }
        
        func textViewDidChange(_ textView: UITextView) {
            if textView.text != parent.placeholder {
                parent.text = textView.text
            }
        }
    }
}

// MARK: - 预览
struct ChineseTextField_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            ChineseTextField(
                text: .constant(""),
                placeholder: "请输入宠物名称"
            )
            .frame(height: 48)
            
            ChineseTextEditor(
                text: .constant(""),
                placeholder: "请输入描述..."
            )
            .frame(height: 120)
        }
        .padding()
    }
}
