import SwiftUI
import Foundation
import Combine
import AVFoundation



// MARK: - 旧的Store定义已移除，现在使用 /Stores/ 目录下集成了后端API的新Store

// 旧的FeedStore定义已移除

// MARK: - AuthStore定义已移除，使用 /Stores/AuthStore.swift

// MARK: - 临时 LoginView (直到项目文件包含正确的 LoginView)
struct LoginView: View {
    @EnvironmentObject var authStore: AuthStore
    @State private var username = "testuser"  // 预填充测试用户名
    @State private var password = "testpass123"  // 预填充测试密码

    var body: some View {
        VStack(spacing: 30) {
            Text("FurryKids")
                .font(.largeTitle)
                .fontWeight(.bold)

            VStack(spacing: 16) {
                TextField("用户名", text: $username)
                    .textFieldStyle(RoundedBorderTextFieldStyle())

                SecureField("密码", text: $password)
                    .textFieldStyle(RoundedBorderTextFieldStyle())

                // 显示错误信息
                if let errorMessage = authStore.errorMessage {
                    Text(errorMessage)
                        .foregroundColor(.red)
                        .font(.caption)
                        .padding(.horizontal)
                }

                Button(authStore.isLoading ? "登录中..." : "登录") {
                    Task {
                        await authStore.login(username: username, password: password)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(authStore.isLoading ? Color.gray : Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
                .disabled(username.isEmpty || password.isEmpty || authStore.isLoading)
            }
            .padding(.horizontal, 40)

            Spacer()
        }
        .padding(.top, 100)
    }
}

// MARK: - 临时 UserProfileView
struct UserProfileView: View {
    @EnvironmentObject var authStore: AuthStore

    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Text("我的")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text("用户: \(authStore.currentUser?.username ?? "未知")")
                    .font(.headline)

                Button("退出登录") {
                    authStore.logout()
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.red)
                .foregroundColor(.white)
                .cornerRadius(10)

                Spacer()
            }
            .padding()
        }
    }
}

// MARK: - 主应用

@main
struct FurryKidsApp: App {
    @StateObject private var authStore = AuthStore()

    var body: some Scene {
        WindowGroup {
            if authStore.isLoading {
                // 启动画面
                SplashView()
            } else if authStore.isAuthenticated {
                // 已登录，显示主应用
                ContentView()
                    .environmentObject(authStore)
            } else {
                // 未登录，显示登录界面
                LoginView()
                    .environmentObject(authStore)
            }
        }
    }
}

// MARK: - 启动画面
struct SplashView: View {
    @State private var connectionStatus = "正在连接服务器..."
    @State private var isConnected = false

    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "pawprint.circle.fill")
                .font(.system(size: 100))
                .foregroundColor(.blue)

            Text("毛孩子AI")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(Color(hex: "#101618"))

            Text(connectionStatus)
                .font(.caption)
                .foregroundColor(isConnected ? .green : .orange)
                .padding(.horizontal)

            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .blue))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(hex: "#F9FAFB"))
        .onAppear {
            testConnection()
        }
    }

    private func testConnection() {
        NetworkManager.shared.testConnection()
            .sink { connected in
                DispatchQueue.main.async {
                    if connected {
                        connectionStatus = "✅ 服务器连接成功"
                        isConnected = true
                    } else {
                        connectionStatus = "❌ 服务器连接失败，请检查网络"
                        isConnected = false
                    }
                }
            }
            .store(in: &cancellables)
    }

    @State private var cancellables = Set<AnyCancellable>()
}

// MARK: - MainContentView已移除，现在使用ContentView.swift中的ContentView



