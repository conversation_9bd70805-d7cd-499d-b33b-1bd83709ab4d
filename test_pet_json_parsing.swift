import Foundation

// 测试宠物JSON解析是否正确
let petJsonString = """
{
  "pets": [
    {
      "id": 16,
      "name": "Birds",
      "breed": "未知品种",
      "age": null,
      "age_display": "未知",
      "gender": "unknown",
      "color": null,
      "size": "medium",
      "weight": null,
      "avatar_url": null,
      "personality": null,
      "personality_tags": [],
      "current_mood": "happy",
      "mood_description": null,
      "response_style": "friendly",
      "interaction_count": 0,
      "experience_points": 0,
      "level": 1,
      "last_interaction_at": null,
      "owner_id": 1,
      "is_active": true,
      "created_at": "2025-07-01T08:20:13",
      "updated_at": "2025-07-01T08:20:13"
    },
    {
      "id": 1,
      "name": "小白",
      "breed": "金毛",
      "age": 24,
      "age_display": "2岁",
      "gender": "male",
      "color": "金黄色",
      "size": "large",
      "weight": 30.5,
      "avatar_url": null,
      "personality": "活泼可爱，喜欢玩球",
      "personality_tags": [
        "活泼",
        "友善",
        "聪明"
      ],
      "current_mood": "happy",
      "mood_description": null,
      "response_style": "friendly",
      "interaction_count": 0,
      "experience_points": 0,
      "level": 1,
      "last_interaction_at": null,
      "owner_id": 1,
      "is_active": true,
      "created_at": "2025-06-25T09:42:23",
      "updated_at": "2025-06-25T09:42:23"
    }
  ],
  "total": 2,
  "page": 1,
  "size": 5,
  "has_next": false
}
"""

// 定义宠物数据模型（与iOS应用中的模型相同）
struct Pet: Codable, Identifiable {
    let id: Int
    let name: String
    let breed: String
    let age: Int?
    let ageDisplay: String
    let gender: String
    let color: String?
    let size: String
    let weight: Double?
    let avatarUrl: String?
    let personality: String?
    let personalityTags: [String]
    let currentMood: String
    let moodDescription: String?
    let responseStyle: String
    let interactionCount: Int
    let experiencePoints: Int
    let level: Int
    let lastInteractionAt: Date?
    let ownerId: Int
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date

    // 编码键映射
    enum CodingKeys: String, CodingKey {
        case id, name, breed, age, gender, color, size, weight, personality, level
        case ageDisplay = "age_display"
        case avatarUrl = "avatar_url"
        case personalityTags = "personality_tags"
        case currentMood = "current_mood"
        case moodDescription = "mood_description"
        case responseStyle = "response_style"
        case interactionCount = "interaction_count"
        case experiencePoints = "experience_points"
        case lastInteractionAt = "last_interaction_at"
        case ownerId = "owner_id"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct PetListResponse: Codable {
    let pets: [Pet]
    let total: Int
    let page: Int
    let size: Int
    let hasNext: Bool
    
    enum CodingKeys: String, CodingKey {
        case pets, total, page, size
        case hasNext = "has_next"
    }
}

// 测试解析
func testPetJSONParsing() {
    let data = petJsonString.data(using: .utf8)!
    
    do {
        let decoder = JSONDecoder()
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        decoder.dateDecodingStrategy = .formatted(dateFormatter)
        
        let response = try decoder.decode(PetListResponse.self, from: data)
        
        print("✅ 宠物JSON解析成功!")
        print("📊 总数: \(response.total)")
        print("📄 页码: \(response.page)")
        print("🐾 宠物数量: \(response.pets.count)")
        
        for (index, pet) in response.pets.enumerated() {
            print("🐕 宠物 \(index + 1):")
            print("   ID: \(pet.id)")
            print("   名称: \(pet.name)")
            print("   品种: \(pet.breed)")
            print("   年龄显示: \(pet.ageDisplay)")
            print("   性别: \(pet.gender)")
            print("   颜色: \(pet.color ?? "未知")")
            print("   体型: \(pet.size)")
            print("   体重: \(pet.weight?.description ?? "未知")")
            print("   性格: \(pet.personality ?? "未知")")
            print("   性格标签: \(pet.personalityTags)")
            print("   当前心情: \(pet.currentMood)")
            print("   等级: \(pet.level)")
            print("   经验值: \(pet.experiencePoints)")
            print("   是否活跃: \(pet.isActive)")
            print("   创建时间: \(pet.createdAt)")
            print("   ---")
        }
        
    } catch {
        print("❌ 宠物JSON解析失败: \(error)")
        if let decodingError = error as? DecodingError {
            switch decodingError {
            case .keyNotFound(let key, let context):
                print("   缺少键: \(key.stringValue)")
                print("   上下文: \(context.debugDescription)")
            case .typeMismatch(let type, let context):
                print("   类型不匹配: \(type)")
                print("   上下文: \(context.debugDescription)")
            case .valueNotFound(let type, let context):
                print("   值未找到: \(type)")
                print("   上下文: \(context.debugDescription)")
            case .dataCorrupted(let context):
                print("   数据损坏: \(context.debugDescription)")
            @unknown default:
                print("   未知解码错误")
            }
        }
    }
}

// 运行测试
testPetJSONParsing()
