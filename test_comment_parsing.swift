#!/usr/bin/env swift

import Foundation

// 定义评论数据模型（与iOS项目中的模型相同）
struct FeedComment: Codable, Identifiable {
    let id: Int
    let feedId: Int
    let userId: Int
    let content: String
    let parentId: Int?
    let isDeleted: Bool
    let isHidden: Bool
    let createdAt: Date
    let updatedAt: Date?

    // 关联数据
    let user: CommentUser?
    let userName: String?
    let userAvatar: String?
    let isReply: Bool
    let replyCount: Int
    let replies: [FeedComment]?

    enum CodingKeys: String, CodingKey {
        case id, content, replies, user
        case feedId = "feed_id"
        case userId = "user_id"
        case parentId = "parent_id"
        case isDeleted = "is_deleted"
        case isHidden = "is_hidden"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case userName = "user_name"
        case userAvatar = "user_avatar"
        case isReply = "is_reply"
        case replyCount = "reply_count"
    }
}

struct CommentUser: Codable {
    let id: Int
    let username: String
    let nickname: String?
    let avatar: String?

    enum CodingKeys: String, CodingKey {
        case id
        case username
        case nickname
        case avatar
    }
}

struct FeedCommentsResponse: Codable {
    let comments: [FeedComment]
    let total: Int
    let page: Int
    let size: Int
    let hasMore: Bool

    enum CodingKeys: String, CodingKey {
        case comments, total, page, size
        case hasMore = "has_more"
    }
}

// 创建日期解码器
func createJSONDecoder() -> JSONDecoder {
    let decoder = JSONDecoder()
    
    // 创建多个日期格式化器来处理不同的日期格式
    let formatters = [
        // 后端实际返回的格式：2025-07-02T06:56:22
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ss"),
        // 带微秒的格式：2025-07-02T06:56:22.123456
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"),
        // 带时区的格式：2025-07-02T06:56:22Z
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ss'Z'"),
        // 带微秒和时区的格式：2025-07-02T06:56:22.123456Z
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"),
        // ISO8601标准格式
        createDateFormatter("yyyy-MM-dd'T'HH:mm:ssZ")
    ]
    
    decoder.dateDecodingStrategy = .custom { decoder in
        let container = try decoder.singleValueContainer()
        let dateString = try container.decode(String.self)
        
        // 尝试使用不同的格式化器解析日期
        for formatter in formatters {
            if let date = formatter.date(from: dateString) {
                return date
            }
        }
        
        // 如果所有格式都失败，抛出错误
        throw DecodingError.dataCorruptedError(
            in: container,
            debugDescription: "无法解析日期字符串: \(dateString)"
        )
    }
    
    return decoder
}

func createDateFormatter(_ format: String) -> DateFormatter {
    let formatter = DateFormatter()
    formatter.dateFormat = format
    formatter.timeZone = TimeZone(secondsFromGMT: 0)
    formatter.locale = Locale(identifier: "en_US_POSIX")
    return formatter
}

// 测试JSON数据（从后端实际响应中提取）
let testJSON = """
{
    "comments": [
        {
            "content": "测试评论API返回格式",
            "id": 44,
            "feed_id": 5,
            "user_id": 1,
            "parent_id": null,
            "is_deleted": false,
            "is_hidden": false,
            "created_at": "2025-07-02T09:01:11",
            "updated_at": "2025-07-02T09:01:11",
            "user_name": "testuser",
            "user_avatar": null,
            "is_reply": false,
            "reply_count": 0,
            "replies": []
        }
    ],
    "total": 35,
    "page": 1,
    "size": 20,
    "has_more": true
}
"""

// 测试解析
do {
    let decoder = createJSONDecoder()
    let data = testJSON.data(using: .utf8)!
    let response = try decoder.decode(FeedCommentsResponse.self, from: data)
    
    print("✅ 解析成功!")
    print("评论数量: \(response.comments.count)")
    if let firstComment = response.comments.first {
        print("第一条评论ID: \(firstComment.id)")
        print("第一条评论内容: \(firstComment.content)")
        print("第一条评论时间: \(firstComment.createdAt)")
    }
} catch {
    print("❌ 解析失败: \(error)")
    if let decodingError = error as? DecodingError {
        switch decodingError {
        case .keyNotFound(let key, let context):
            print("🔑 缺少键: \(key.stringValue), 路径: \(context.codingPath)")
        case .typeMismatch(let type, let context):
            print("🔄 类型不匹配: \(type), 路径: \(context.codingPath)")
        case .valueNotFound(let type, let context):
            print("❓ 值不存在: \(type), 路径: \(context.codingPath)")
        case .dataCorrupted(let context):
            print("💥 数据损坏: \(context.debugDescription), 路径: \(context.codingPath)")
        @unknown default:
            print("❓ 未知解码错误")
        }
    }
}
